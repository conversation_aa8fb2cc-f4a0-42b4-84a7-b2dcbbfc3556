package com.bilibili.miniapp.open.service.biz.applet;

import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;

import java.util.List;
import java.util.Map;

public interface IAppletSeasonService {

    /**
     * 通过season(包含season授权的小程序列表)列表获取合适的小程序
     * @param appletShortBos
     * @return
     */
    List<MiniAppBaseInfoBo> batchFetchAppletFromCandidates(List<AppletShortBo> appletShortBos);

    /**
     * 通过season(包含season授权的小程序列表)获取合适的小程序
     * @param appletShortBo
     * @return
     */
    MiniAppBaseInfoBo fetchAppletFromCandidates(AppletShortBo appletShortBo);

    Map<Long, String> getDefaultAppletBySeasonIds(List<Long> seasonIds);

    List<Long> getSeasonIdsByAppId(String appId);

    void bindSeasonToApplet(Long seasonId, String appId);

    List<String> getAppletPriority();

    void saveAppletPriority(List<String> appIdList);

    Map<Long, String> getDefaultAppletBySeasonIdsFromCache(List<Long> seasonIds);

    List<String> getAppletPriorityFromCache();
}
