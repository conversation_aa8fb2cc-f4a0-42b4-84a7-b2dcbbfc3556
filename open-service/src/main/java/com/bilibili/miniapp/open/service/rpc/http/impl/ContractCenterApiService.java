package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.ContractCenterApi;
import com.bilibili.miniapp.open.service.rpc.http.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@Service
@Slf4j
public class ContractCenterApiService extends AbstractOpenService {

    @Autowired
    private ContractCenterApi contractCenterApi;


    public CreateContractResult createContract(CreateContractRequest request) {
        log.info("[ContractCenterApiService] 创建结算合同, request={}", JSON.toJSONString(request));
        CreateContractResult result = call("创建结算合同", contractCenterApi::createContract, request);
        log.info("[ContractCenterApiService] 创建结算合同成功, result={}", JSON.toJSONString(result));
        return result;
    }

    public void fillContract(FillContractDto fillContractDto) {
        call("填写合同", contractCenterApi::fillContract, fillContractDto);
    }


    public ContractSignUrlResult getContractSignUrl(String contractId) {
        return call("获取合同签署链接", () -> contractCenterApi.getSignUrl(contractId, "乙方"));
    }

    public ContractFormDto getContractForm(String contractId) {
        return call("获取合同表单", () -> contractCenterApi.getFormInfo(contractId));
    }


    public ContractDetailDto getContractDetail(String contractId) {
        ContractDetailListDto result = call("获取合同详情", () -> contractCenterApi.getContractDetails(contractId, "小程序开放平台"));
        if (CollectionUtils.isEmpty(result.getList())) {
            return null;
        }

        return result.getList().get(0);
    }

    public SupplierResultDto getSupplierInfo(String companyName) {
        return call("获取供应商信息", () -> contractCenterApi.getSupplierInfo(companyName));

    }

}
