package com.bilibili.miniapp.open.service.databus.entity;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;

import java.util.List;

/**
 * 结构无文档，由合同中台开发提供
 *
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class ContractMsg {

    private String requestId;

    private String contractId;

    private Integer state;

    private String stage;

    private List<Long> mid;

    /**
     * 时间戳，秒
     */
    private Long timestamp;

    private String entity;

    /**
     * 时间戳，秒
     */
    private Long cTime;

    private String cCreator;
}

