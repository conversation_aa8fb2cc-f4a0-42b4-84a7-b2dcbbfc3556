package com.bilibili.miniapp.open.service.mapper;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailExtra;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Mapper
public interface IncomeBizMapper {

    IncomeBizMapper MAPPER = Mappers.getMapper(IncomeBizMapper.class);

    @Mapping(target = "incomeDate", source = "logDate")
    @Mapping(target = "distributableRatio", ignore = true)
    @Mapping(target = "channelFeeRatio", ignore = true)
    @Mapping(target = "appName", ignore = true)
    IncomeDetailBo toBo(MiniAppOpenIaaIncomeDetailPo po);


    default IncomeDetailBo toBo(MiniAppOpenIaaIncomeDetailPo po, Map<String, String> appNameMap) {
        IncomeDetailBo bo = toBo(po);
        bo.setAppName(appNameMap.get(bo.getAppId()));
        IncomeDetailExtra incomeDetailExtra = JSON.parseObject(po.getExtra(), IncomeDetailExtra.class);
        if (incomeDetailExtra != null) {
            bo.setDistributableRatio(incomeDetailExtra.getDistributableRatio());
            bo.setChannelFeeRatio(incomeDetailExtra.getChannelFeeRatio());
        }
        return bo;
    }
}
