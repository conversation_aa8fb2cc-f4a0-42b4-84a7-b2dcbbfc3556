package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.rpc.http.dto.*;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@RESTClient(name = "contract-center", host = "discovery://main.archive.up-sign-admin")
public interface ContractCenterApi {

    /**
     * <a href="https://cloud.bilibili.co/akali/appsManage?appId=main.archive.up-sign-admin&level=2&itemId=20505&appVersion=undefined#sh/sh001/prod">创建合同</a>
     *
     * @see com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractRequest
     */
    @POST("/x/internal/up-sign-admin/contract/create")
    BiliCall<Response<CreateContractResult>> createContract(@Body RequestBody body);

    /**
     * <a href="https://cloud.bilibili.co/akali/appsManage?appId=main.archive.up-sign-admin&level=2&itemId=20123&appVersion=undefined#sh/sh001/prod">填写合同</a>
     */
    @POST("/x/internal/up-sign-admin/contract/fill/complete")
    BiliCall<Response<Void>> fillContract(@Body RequestBody body);

    /**
     * <a href="https://cloud.bilibili.co/akali/appsManage?appId=main.archive.up-sign-admin&level=2&itemId=163011&appVersion=undefined#sh/sh001/prod">获取签署url</a>
     */
    @GET("/x/internal/up-sign-admin/contract/entity/sign/url")
    BiliCall<Response<ContractSignUrlResult>> getSignUrl(@Query("contract_id") String contactId,
                                                         @Query("entity_partner") String entityPartner);


    /**
     * <a href="https://cloud.bilibili.co/akali/appsManage?appId=main.archive.up-sign-admin&level=2&itemId=20297&appVersion=undefined#sh/sh001/prod">获取待填写内容信息</a>
     */
    @GET("/x/internal/up-sign-admin/contract/fill")
    BiliCall<Response<ContractFormDto>> getFormInfo(@Query("contract_id") String contactId);


    /**
     * <a href="https://cloud.bilibili.co/akali/appsManage?appId=main.archive.up-sign-admin&level=2&itemId=19899&appVersion=undefined#sh/sh001/prod">获取合同详情</a>
     */
    @GET("/x/internal/up-sign-admin/contracts")
    BiliCall<Response<ContractDetailListDto>> getContractDetails(@Query("contract_ids") String contactIds,
                                                                 @Query("businesses") String businesses);


    /**
     * <a href="https://cloud.bilibili.co/akali/appsManage?appId=main.archive.up-sign-admin&level=2&itemId=19862&appVersion=undefined#sh/sh001/prod">获取供应商id</a>
     */
    @GET("/x/internal/up-sign-admin/supplier/enterprise/check")
    BiliCall<Response<SupplierResultDto>> getSupplierInfo(@Query("enterprise_name") String companyName);

}
