package com.bilibili.miniapp.open.service.bo.applet;


import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import lombok.Data;

import java.util.List;

/**
 * @author: huan
 * @Date: 2025-06-27 16:01
 * @Description: 小程序短剧信息
 */

@Data
public class AppletSeasonBo {

    // 小程序剧集信息
    private AppletSeasonInfoBo seasonBo;

    // 剧集授权的所有小程序信息
    private List<CandidateApplet> candidateApplets;

    @Data
    public static class CandidateApplet {
        private MiniAppBaseInfoBo candidate;
        private boolean defaultTag; // 是否为默认标签
    }
}
