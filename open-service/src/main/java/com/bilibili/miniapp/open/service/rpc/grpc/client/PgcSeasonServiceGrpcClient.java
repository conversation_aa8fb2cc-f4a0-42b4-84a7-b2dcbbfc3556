package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bapis.pgc.servant.season.season.SearchSeasonReply;
import com.bapis.pgc.servant.season.season.SearchSeasonReq;
import com.bapis.pgc.servant.season.season.SortCriteria;
import com.bapis.pgc.servant.season.season.SortOrder;
import com.bapis.pgc.service.season.season.*;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SectionBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.enums.SeasonPaymentStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int64Value;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 接口文档
 * <a href="https://doc.weixin.qq.com/doc/w3_Ae0A9watADc09zvcX2kQAy06SsLdB?scode=ANYAEAdoABEANLnJpgAKwAIAYNAAY">query4short.</a>
 * <a href="https://git.bilibili.co/bapis/bapis/-/blob/master/pgc/servant/season/season/Season2Service.proto#L19">根据mid查询seasons</a>
 * <AUTHOR>
 * @date 2024/12/26 15:46
 */
@Slf4j
@Component
public class PgcSeasonServiceGrpcClient extends AbstractGrpcClient {
    @RPCClient("season.service")
    private Season2Grpc.Season2BlockingStub season2BlockingStub;

    @RPCClient("ogv.season.servant")
    private com.bapis.pgc.servant.season.season.Season2Grpc.Season2BlockingStub servantSeasonBlockingStub;

    @Autowired
    private ConfigCenter configCenter;

    /**
     * 在ogv那走的是数据库
     */
    public SearchSeasonReply querySeasons(long mid, Page page) {
        SearchSeasonReq req = SearchSeasonReq.newBuilder()
                .setPn(page.getPage())
                .setPs(page.getPageSize())
                .setUpMid(Int64Value.of(mid))
                .setSeasonType(com.bapis.pgc.servant.season.season.SeasonTypeEnum.SHORT_PLAY)
                .addPublishStatus(com.bapis.pgc.servant.season.season.SeasonPublishStatusEnum.ONLINE_SEASON_STATUS)
                .addChannel(com.bapis.pgc.servant.season.season.Channel.MINI_PROGRAM_CHANNEL)
                .addSortCriteria(SortCriteria.newBuilder()
                        //目前可选值传的只有“create_time”，按剧集创建时间排序
                        .setField("create_time")
                        .setOrder(SortOrder.DESC)
                        .build())
                .build();
        return servantSeasonBlockingStub.searchSeason(req);
    }

    public SearchSeasonReply querySeasons(long mid, Page page, String seasonName, GrpcCallContext context) {
        SearchSeasonReq.Builder builder = SearchSeasonReq.newBuilder()
                .setPn(page.getPage())
                .setPs(page.getPageSize())
                .setUpMid(Int64Value.of(mid))
                .setSeasonType(com.bapis.pgc.servant.season.season.SeasonTypeEnum.SHORT_PLAY)
                .addPublishStatus(com.bapis.pgc.servant.season.season.SeasonPublishStatusEnum.ONLINE_SEASON_STATUS)
                .addChannel(com.bapis.pgc.servant.season.season.Channel.MINI_PROGRAM_CHANNEL)
                .addSortCriteria(SortCriteria.newBuilder()
                        //目前可选值传的只有“create_time”，按剧集创建时间排序
                        .setField("create_time")
                        .setOrder(SortOrder.DESC)
                        .build());
        if (StringUtils.isNotBlank(seasonName)) {
            builder.setTitle(seasonName);
        }
        return withOptions(servantSeasonBlockingStub, context).searchSeason(builder.build());
    }

    public List<SeasonBo> querySeason4Short(List<Long> seasonIdList) {
        return this.querySeason4Short(seasonIdList, GrpcCallContext.builder().timeout(configCenter.getShortPlay().getSeasonTimeout()).build());
    }

    /**
     * 根据ssid查询剧信息
     * PS：
     * 1、本接口是短剧season接口，不是通用的ogv season接口
     * 2、不会返回集信息，即{@link SeasonBo#getEpisodes()}为null
     * 3、作者信息只包含mid
     */
    public List<SeasonBo> querySeason4Short(List<Long> seasonIdList, GrpcCallContext context) {
        if (CollectionUtils.isEmpty(seasonIdList)) {
            return Collections.emptyList();
        }

        return FunctionUtil.batch(seasonIdList, subSeasonIdList -> {
            QuerySeasonDetailsByConditionReq request = QuerySeasonDetailsByConditionReq.newBuilder()
                    .addAllSeasonIds(subSeasonIdList)
                    .setCommonFilter(VisibilityFilter.newBuilder().setChannel(Channel.CHANNEL_MINI_PROGRAM).build())
                    .addAllQueryConditions(Lists.newArrayList(QuerySeasonDetailsExtraConditionEnum.SEASON_SECTION_INFO,
                            QuerySeasonDetailsExtraConditionEnum.SEASON_PRODUCER_INFO,
                            QuerySeasonDetailsExtraConditionEnum.MEDIA_BASE_INFO))
                    .build();
            QuerySeasonDetailsByConditionReply response;
            String requestJson = null;
            try {
                requestJson = JsonFormat.printer().print(request);
                log.info("[PgcSeasonServiceGrpcClient] querySeasonDetailsByCondition request={}", requestJson);
                response = withOptions(season2BlockingStub, context).querySeasonDetailsByCondition(request);
                List<SeasonDetailInfo> seasonDetailsList = response.getSeasonDetailsList();
                return seasonDetailsList.stream()
                        .filter(sd -> {
                            boolean isOnline = Objects.equals(sd.getMiniProgramVisibility().getProgramPublishStatus(), SeasonPublishStatusEnum.ONLINE);
                            boolean isMiniApp = sd.getChannelSeasonCommonList().stream().anyMatch(csc -> Objects.equals(csc.getChannel(), Channel.CHANNEL_MINI_PROGRAM));
                            return isOnline && isMiniApp;
                        })
                        .map(sd -> {
                            SeasonBaseInfo baseInfo = sd.getBaseInfo();
                            List<SectionBo> sectionBoList = sd.getSectionsList().stream()
                                    .filter(section -> Objects.equals(section.getSectionType(), SectionTypeEnum.FORMAL))
                                    .map(section -> SectionBo.builder()
                                            .sectionId(section.getSectionId())
                                            .sectionType(section.getSectionTypeValue())
                                            .build())
                                    .collect(Collectors.toList());
                            PaymentStatusEnum paymentStatusEnum = sd.getChannelSeasonCommonList().stream()
                                    .filter(channel -> Objects.equals(channel.getChannel(), Channel.CHANNEL_MINI_PROGRAM))
                                    .map(ChannelSeasonCommon::getPaymentStatus)
                                    .findAny()
                                    .orElse(null);
                            return SeasonBo.builder()
                                    .seasonId(baseInfo.getSeasonId())
                                    .cover(baseInfo.getCover())
                                    .title(baseInfo.getTitle())
                                    .subTitle(baseInfo.getSubTitle())
                                    .isFinish(baseInfo.getIsFinish())
                                    .paymentStatus(SeasonPaymentStatus.getByRemotePaymentStatus(paymentStatusEnum).getCode())
                                    .styles(new ArrayList<>(sd.getMediaBaseInfo().getStylesList()))
                                    .author(UserInfoBo.builder()
                                            .mid(sd.getSeasonProducerCount() == 0 ? 0L :
                                                    sd.getSeasonProducer(0).getMid())
                                            .build())
                                    .sections(sectionBoList)
                                    .build();
                        }).collect(Collectors.toList());
            } catch (Exception e) {
                log.error("[PgcSeasonServiceGrpcClient] querySeasonDetailsByCondition error, request={}", requestJson, e);
                throw new RuntimeException(e);
            }
        }, 50);
    }

    /**
     * 根据分节section_id查询集信息
     * PS：本接口是短剧episode接口，不是通用的ogv episode接口
     */
    public Map<Long, List<EpisodeBo>> queryEpisode4Short(List<Long> sectionIdList, GrpcCallContext context) {
        if (CollectionUtils.isEmpty(sectionIdList)) {
            return Maps.newHashMap();
        }
        return FunctionUtil.batch(sectionIdList, subSectionIdList -> {
                    QueryEpisodesBySectionIdsReq request = QueryEpisodesBySectionIdsReq.newBuilder()
                            .addAllSectionIds(subSectionIdList)
                            .setCommonFilter(VisibilityFilter.newBuilder().setChannel(Channel.CHANNEL_MINI_PROGRAM).build())
                            .build();
                    QueryEpisodesBySectionIdsReply response;
                    String requestJson = null;
                    try {
                        requestJson = JsonFormat.printer().print(request);
                        log.info("[PgcSeasonServiceGrpcClient] queryEpisode4Short request={}", requestJson);
                        response = withOptions(season2BlockingStub, context).queryEpisodesBySectionIds(request);
                        Map<Long, EpisodeDetailList> sectionEpisodeDetailMap = response.getEpisodeDetailListMap();
                        return sectionEpisodeDetailMap.entrySet().stream()
                                .map(en -> Pair.of(en.getKey(), convertEpisodeList(en.getValue())))
                                .collect(Collectors.toList());
                    } catch (Exception e) {
                        log.error("[PgcSeasonServiceGrpcClient] queryEpisode4Short error, request={}", requestJson, e);
                        throw new RuntimeException(e);
                    }
                }, 20)
                .stream()
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, FunctionUtil.override()));
    }

    public Map<Long, Boolean> queryEpisodesVisibilityByAids(List<Long> aids, GrpcCallContext context) {
        if (CollectionUtils.isEmpty(aids)) {
            return Maps.newHashMap();
        }
        QueryEpisodesVisibilityByAidsReq req = QueryEpisodesVisibilityByAidsReq.newBuilder().addAllAids(aids).setChannel(Channel.CHANNEL_MINI_PROGRAM).build();
        try {
            log.info("[PgcSeasonServiceGrpcClient] queryEpisodesVisibilityByAids request={}", JsonFormat.printer().print(req));
            QueryEpisodesVisibilityByAidsReply reply = withOptions(season2BlockingStub, context).queryEpisodesVisibilityByAids(req);
            log.info("[PgcSeasonServiceGrpcClient] queryEpisodesVisibilityByAids response={}", JsonFormat.printer().print(reply));
            return reply.getEpisodesVisibilityMap();
        } catch (Exception e) {
            log.error("[PgcSeasonServiceGrpcClient] queryEpisodesVisibilityByAids error, request={}", aids, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }
    }

    public List<EpisodeBo> queryEpisodeByAids(List<Long> aids, GrpcCallContext context) {
        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyList();
        }
        QueryEpisodesByAidsReq req = QueryEpisodesByAidsReq.newBuilder()
                .addAllAids(aids)
                .setCommonFilter(VisibilityFilter.newBuilder().setChannel(Channel.CHANNEL_MINI_PROGRAM).build())
                .build();
        try {
            log.info("[PgcSeasonServiceGrpcClient] queryEpisodeByAids request={}", JsonFormat.printer().print(req));
            QueryEpisodesByAidsReply reply = withOptions(season2BlockingStub, context).queryEpisodesByAids(req);
            log.info("[PgcSeasonServiceGrpcClient] queryEpisodeByAids response={}", JsonFormat.printer().print(reply));
            Map<Long, EpisodeDetailInfo> sectionEpisodeDetailMap = reply.getAidToEpisodeDetailInfoMap();
            return new ArrayList<>(convertEpisodeList(Lists.newArrayList(sectionEpisodeDetailMap.values())));
        } catch (Exception e) {
            log.error("[PgcSeasonServiceGrpcClient] queryEpisodeByAids error, request={}", aids, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }
    }
    
    public List<EpisodeBo> queryEpisodeByEpIds(List<Long> episodeIds, GrpcCallContext context) {
        if (CollectionUtils.isEmpty(episodeIds)) {
            return Collections.emptyList();
        }
        QueryEpisodesByEpIdsReq req = QueryEpisodesByEpIdsReq.newBuilder()
                .addAllEpisodeIds(episodeIds)
                .setCommonFilter(VisibilityFilter.newBuilder().setChannel(Channel.CHANNEL_MINI_PROGRAM).build())
                .build();
        try {
            log.info("[PgcSeasonServiceGrpcClient] queryEpisodeByEpIds request={}", JsonFormat.printer().print(req));
            QueryEpisodesByEpIdsReply reply = withOptions(season2BlockingStub, context).queryEpisodesByEpIds(req);
            log.info("[PgcSeasonServiceGrpcClient] queryEpisodeByEpIds response={}", JsonFormat.printer().print(reply));
            return new ArrayList<>(convertEpisodeList(Lists.newArrayList(reply.getEpisodeDetailsList())));
        } catch (Exception e) {
            log.error("[PgcSeasonServiceGrpcClient] queryEpisodeByEpIds error, request={}", episodeIds, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }
    }

    private EpisodeBo convertEpisode(EpisodeDetailInfo episodeDetailInfo) {
        EpisodeBaseInfo baseInfo = episodeDetailInfo.getEpisodeBaseInfo();
        return EpisodeBo.builder()
                .seasonId(baseInfo.getSeasonId())
                .episodeId(baseInfo.getEpisodeId())
                .ord(baseInfo.getOrd())
                .title(baseInfo.getTitle())
                .longTitle(baseInfo.getLongTitle())
                .cover(baseInfo.getCover())
                .aid(baseInfo.getAid())
                .cid(baseInfo.getCid())
                .build();
    }

    private List<EpisodeBo> convertEpisodeList(List<EpisodeDetailInfo> episodeDetailInfos) {
        return episodeDetailInfos.stream()
                .filter(detail -> {
                    boolean isOnline = Objects.equals(detail.getMiniProgramEpisodeVisibility().getProgramPublishStatus(), EpisodePublishStatusEnum.EP_ONLINE);
                    boolean isMiniApp = detail.getChannelEpisodeCommonList().stream().anyMatch(csc -> Objects.equals(csc.getChannel(), Channel.CHANNEL_MINI_PROGRAM));
                    return isOnline && isMiniApp;
                })
                .map(detail -> {
                    EpisodeBaseInfo baseInfo = detail.getEpisodeBaseInfo();
                    PaymentStatusEnum paymentStatusEnum = detail.getChannelEpisodeCommonList().stream()
                            .filter(channel -> Objects.equals(channel.getChannel(), Channel.CHANNEL_MINI_PROGRAM))
                            .map(ChannelEpisodeCommon::getPaymentStatus)
                            .findAny()
                            .orElse(null);
                    return EpisodeBo.builder()
                            .seasonId(baseInfo.getSeasonId())
                            .episodeId(baseInfo.getEpisodeId())
                            .ord(baseInfo.getOrd())
                            .title(baseInfo.getTitle())
                            .longTitle(baseInfo.getLongTitle())
                            .cover(baseInfo.getCover())
                            .aid(baseInfo.getAid())
                            .cid(baseInfo.getCid())
                            .paymentStatus(SeasonPaymentStatus.getByRemotePaymentStatus(paymentStatusEnum).getCode())
                            .duration(baseInfo.getDuration())
                            .build();
                }).collect(Collectors.toList());
    }


    private List<EpisodeBo> convertEpisodeList(EpisodeDetailList episodeDetailList) {
        return convertEpisodeList(episodeDetailList.getEpisodeDetailsList());
    }
}
