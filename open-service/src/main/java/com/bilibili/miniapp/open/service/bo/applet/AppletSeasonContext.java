package com.bilibili.miniapp.open.service.bo.applet;


import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: huan
 * @Date: 2025-06-30 20:06
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppletSeasonContext {

    // 剧集信息
    private PageResult<SeasonBo> seasonPageResult;

    //

}
