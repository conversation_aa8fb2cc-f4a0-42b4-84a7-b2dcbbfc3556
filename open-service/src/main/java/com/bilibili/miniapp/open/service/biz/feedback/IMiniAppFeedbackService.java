package com.bilibili.miniapp.open.service.biz.feedback;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppFeedbackDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppFeedbackQuery;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/11
 **/
public interface IMiniAppFeedbackService {

    /**
     * 获取小程序反馈列表
     *
     * @param miniAppFeedbackQuery 查询结构体
     * @return 成功 true 失败 false
     */
    PageResult<MiniAppFeedbackDTO> getMiniAppFeedbackDTOS(MiniAppFeedbackQuery miniAppFeedbackQuery);
}
