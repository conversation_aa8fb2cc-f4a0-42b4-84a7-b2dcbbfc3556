package com.bilibili.miniapp.open.service.bo.applet;


import com.bilibili.miniapp.open.common.entity.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: huan
 * @Date: 2025-06-27 15:56
 * @Description: 剧集查询参数
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthorSeasonQueryParam {
    // 剧作者id
    private Long mid;

    // 剧id
    private Long seasonId;

    // 小程序appId
    private String appId;

    // 分页信息
    private Page page;
}
