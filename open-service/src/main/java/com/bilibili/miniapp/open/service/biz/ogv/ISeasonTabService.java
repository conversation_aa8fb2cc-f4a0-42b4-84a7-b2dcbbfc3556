package com.bilibili.miniapp.open.service.biz.ogv;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
public interface ISeasonTabService {

    /**
     * @see com.bilibili.miniapp.open.common.enums.SeasonTabType
     */
    void addSeasonToTab(String appId, int tabType, Map<Long, SeasonBo> seasonMap);

    void removeSeasonFromTab(String appId, int tabType, Long seasonId);

    void removeMidSeason(String appId, long mid);

    PageResult<Long> queryTabSeasonIds(String appId, int tabType, Page page);

    List<Long> getAlreadyPublishedSeasonIds(String appId, List<Long> seasonIds);

    void cacheAllSeasonTab();

    Map<Integer, List<Long>> getAllSeasonTabFromCache(String appId);
}
