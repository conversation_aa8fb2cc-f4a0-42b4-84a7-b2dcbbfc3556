package com.bilibili.miniapp.open.service.bo.contract;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 填写合同内容
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FillContractBo {

    /**
     * 小程序ID
     */
    private String appId;

    private String appName;

    /**
     * 签约人姓名
     */
    private String signatoryName;

    /**
     * 签约人手机号
     */
    private String signatoryPhone;

    /**
     * 签约人电子邮箱
     */
    private String signatoryEmail;

    /**
     * 联系地址
     */
    private String contactAddress;

    private String companyName;

    private String startYear;

    private String startMonth;

    private String startDay;

    private String signYear;

    private String signMonth;

    private String signDay;

    private String endYear;

    private String endMonth;

    private String endDay;
}
