package com.bilibili.miniapp.open.service.biz.account.impl;

import com.bapis.account.service.Info;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.client.UserInfoServiceGrpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Service
public class AccountService implements IAccountService {

    @Autowired
    private UserInfoServiceGrpcClient userInfoServiceGrpcClient;

    @Override
    public AccountInfoBo getAccountInfo(long mid) {
        Map<Long, Info> longInfoMap = userInfoServiceGrpcClient.infoMap(List.of(mid));
        AccountInfoBo accountInfoBo = new AccountInfoBo();
        if (longInfoMap.containsKey(mid)) {
            Info info = longInfoMap.get(mid);
            accountInfoBo.setFace(info.getFace());
            accountInfoBo.setNickName(info.getName());
            accountInfoBo.setMid(mid);
        }
        return accountInfoBo;
    }


    @Override
    public List<AccountInfoBo> getAccountInfos(List<Long> midList) {
        List<AccountInfoBo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(midList)) {
            return result;
        }
        Map<Long, Info> map = userInfoServiceGrpcClient.infoMap(midList);

        map.forEach((mid, info) -> {
            AccountInfoBo accountInfoBo = new AccountInfoBo();
            accountInfoBo.setFace(info.getFace());
            accountInfoBo.setNickName(info.getName());
            accountInfoBo.setMid(mid);
            result.add(accountInfoBo);
        });
        return result;
    }
}
