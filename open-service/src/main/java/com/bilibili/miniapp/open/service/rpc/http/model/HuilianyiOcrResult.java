package com.bilibili.miniapp.open.service.rpc.http.model;

import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * {@see https://opendocs.huilianyi.com/implement/business-data/receipt/receipt-ocr-open.html?h=ocr}
 * prompt:  根据这个地址  https://opendocs.huilianyi.com/implement/business-data/receipt/receipt-ocr-open.html?h=ocr
 * 生成java对象， 并使用@Data@Accessors(chain = true)注解标记，并生成注释
 *
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class HuilianyiOcrResult {

    /// TODO
    private InvoiceData data;


    // TODO
    public List<String> getBusinessName() {

        return getData().getIdentify_results()
                .stream()
                .map(OcrResult::getDetails)
                .map(InvoiceDetails::getSeller)
                .collect(Collectors.toList());
    }


    @Data
    @Accessors(chain = true)
    public static class InvoiceData {


        private List<OcrResult> identify_results;


    }

    /**
     * 发票识别结果
     */
    @Data
    @Accessors(chain = true)
    public static class OcrResult {
        /**
         * 发票类型代码（10101:增值税发票）
         */
        private String type;

        /**
         * 图片旋转角度
         */
        private Integer orientation;

        /**
         * 发票区域坐标[x1,y1,x2,y2]
         */
        private List<Integer> region;

        /**
         * 发票详细信息
         */
        private InvoiceDetails details;

        /**
         * 辅助识别信息
         */
        private ExtraInfo extra;

        private SlicingAttachment slicingAttachment;
    }

    /**
     * 发票详细信息（根据类型不同字段不同）
     */
    @Data
    @Accessors(chain = true)
    public static class InvoiceDetails {
        // 通用字段
        private String invoiceTypeNo;
        private String code;
        private String number;

        // 增值税专用发票字段
        private String machinePrintedCode;
        private String machinePrintedNumber;
        private String date;
        private String pretaxAmount;
        private String total;
        private String tax;
        private String checkCode;
        private String seller;
        private String sellerTaxId;
        private String buyer;
        private String buyerTaxId;
        private String companySeal;

        // 出租车票字段
        private String timeGeton;
        private String timeGetoff;
        private String mileage;

        // 火车票字段
        private String stationGeton;
        private String stationGetoff;
        private String trainNumber;

        // 其他字段...
        private List<InvoiceItem> items;
        private List<FlightInfo> flights;

        private String electronicMark;
    }

    /**
     * 商品/服务条目
     */
    @Data
    @Accessors(chain = true)
    public static class InvoiceItem {
        private String name;
        private String specification;
        private String unit;
        private String quantity;
        private String price;
        private String total;
        private String taxRate;
        private String tax;
    }

    /**
     * 航班信息（用于行程单）
     */
    @Data
    @Accessors(chain = true)
    public static class FlightInfo {
        private String from;
        private String to;
        private String flightNumber;
        private String date;
        private String time;
        private String seat;
        private String carrier;
    }

    /**
     * 辅助识别信息
     */
    @Data
    @Accessors(chain = true)
    public static class ExtraInfo {
        // 校验码备选
        private List<String> checkCodeCandidates;
        private List<String> checkCodeLastSix;

        // 二维码/条码
        private List<String> qrcode;
        private List<String> barcode;
    }

    /**
     * 切图附件信息
     */
    @Data
    @Accessors(chain = true)
    public static class SlicingAttachment {
        private String id;
        private String attachmentOID;
        private String fileName;
        private String fileType;
        private String fileURL;
        private Long size;
        // 其他字段根据实际需要添加...
    }
}
