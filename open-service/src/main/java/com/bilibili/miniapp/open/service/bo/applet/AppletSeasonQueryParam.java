package com.bilibili.miniapp.open.service.bo.applet;

import com.bilibili.miniapp.open.common.entity.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppletSeasonQueryParam {
    private String appId;

    private String seasonName;

    private boolean appendEpisode;

    private Integer sourceFrom;

    private Page page;
}
