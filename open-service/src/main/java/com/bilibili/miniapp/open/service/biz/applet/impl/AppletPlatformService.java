package com.bilibili.miniapp.open.service.biz.applet.impl;


import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.common.util.PageUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.applet.IAppletPlatformService;
import com.bilibili.miniapp.open.service.biz.applet.IAppletSeasonService;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationExtService;
import com.bilibili.miniapp.open.service.bo.applet.*;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.bo.ogv.SectionBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.client.MainSiteAccountServiceGrpcClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huan
 * @Date: 2025-06-30 18:02
 * @Description:
 */

@Service
@Slf4j
public class AppletPlatformService implements IAppletPlatformService {

    @Resource
    private IOgvSeasonService ogvSeasonService;

    @Resource
    private IMiniAppService miniAppService;

    @Resource
    private IAppletUrlService appletUrlService;

    @Resource
    private ISeasonAuthorizationExtService seasonAuthorizationExtService;

    @Resource
    private IAppletSeasonService appletSeasonService;

    @Resource
    private IAuthorAuthorizationService authorAuthorizationService;

    @Resource
    private MainSiteAccountServiceGrpcClient mainSiteAccountServiceGrpcClient;

    @Resource(name = "redissonCacheRepository")
    private ICacheRepository cacheRepository;

    @Override
    public PageResult<AppletSeasonInfoBo> getAppletSeasonInfoList(AppletSeasonQueryParam queryParam) {
        String appId = queryParam.getAppId();
        AssertUtil.hasText(appId, ErrorCodeType.BAD_PARAMETER.getCode(), "appId不能为空");
        // 获取小程序信息
        Integer sourceFrom = queryParam.getSourceFrom();

        boolean appendEpisode = queryParam.isAppendEpisode();
        PageResult<SeasonBo> seasonBoPageResult = ogvSeasonService.searchSeasons(appId, queryParam.getPage(), queryParam.getSeasonName(), appendEpisode);
        int total = seasonBoPageResult.getTotal();
        List<SeasonBo> records = seasonBoPageResult.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            log.info("[AppletPlatformService] getAppletSeasonInfoList no seasons found for appId: {}", appId);
            return PageResult.emptyPageResult();
        }
        List<AppletSeasonUrlParam.SeasonEpIds> seasonEpIds = new ArrayList<>();
        for (SeasonBo record : records) {
            AppletSeasonUrlParam.SeasonEpIds seasonEpId = new AppletSeasonUrlParam.SeasonEpIds();
            seasonEpId.setSeasonId(record.getSeasonId());
            if (appendEpisode && CollectionUtils.isNotEmpty(record.getSections())) {
                List<Long> episodeIds = record.getSections().stream()
                        .filter(section -> CollectionUtils.isNotEmpty(section.getEpisodes()))
                        .flatMap(section -> section.getEpisodes().stream())
                        .map(EpisodeBo::getEpisodeId)
                        .distinct()
                        .collect(Collectors.toList());
                seasonEpId.setEpisodeIds(episodeIds);
            }
            seasonEpIds.add(seasonEpId);
        }
        MiniAppBaseInfoBo miniAppBaseInfo = miniAppService.getMiniAppBaseInfo(appId, sourceFrom, false);
        if (miniAppBaseInfo == null) {
            log.error("[AppletPlatformService] getAppletSeasonInfoList failed, miniAppBaseInfo not found for appId: {}", appId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "小程序信息不存在");
        }
        List<AppletSeasonUrl> seasonUrls = appletUrlService.getSingleAppletSeasonUrls(miniAppBaseInfo, seasonEpIds, sourceFrom);
        if (CollectionUtils.isEmpty(seasonUrls)) {
            log.info("[AppletPlatformService] getAppletSeasonInfoList no seasonUrls found for appId: {}", appId);
            return PageResult.emptyPageResult();
        }
        Map<String, AppletSeasonUrl> seasonUrlMap = seasonUrls.stream().filter(seasonUrl -> NumberUtil.isPositive(seasonUrl.getSeasonId()))
                .collect(Collectors.toMap(appletSeasonUrl -> AppletSeasonUrl.buildSeasonKey(appletSeasonUrl.getAppId(), appletSeasonUrl.getSeasonId()), Function.identity(), FunctionUtil.override()));

        List<AppletSeasonInfoBo> appletSeasonInfoBos = records.stream().map(seasonBo -> seasonBoToAppletSeasonInfoBo(seasonBo, miniAppBaseInfo, seasonUrlMap)).filter(Objects::nonNull).collect(Collectors.toList());
        return new PageResult<>(total, appletSeasonInfoBos);
    }

    @Override
    public PageResult<AppletSeasonBo> getAppletSeasonList(AuthorSeasonQueryParam queryParam) throws Exception {
        Long mid = queryParam.getMid();
        AssertUtil.isTrue(NumberUtil.isPositive(mid), ErrorCodeType.BAD_PARAMETER.getCode(), "mid必须为正整数");
        List<String> appletPriority = appletSeasonService.getAppletPriority();
        Map<Long, List<SeasonAuthorizationResultBo>> appIdAuthorizationMapping = seasonAuthorizationExtService.getAppIdsByMidIn(List.of(queryParam.getMid()));
        if (MapUtils.isEmpty(appIdAuthorizationMapping) || CollectionUtils.isEmpty(appIdAuthorizationMapping.get(mid))) {
            log.info("[AppletPlatformService] getAppletSeasonList no authorized appIds found for mid: {}", mid);
            return PageResult.emptyPageResult();
        }
        List<String> appIds = appIdAuthorizationMapping.values().stream()
                .flatMap(Collection::stream)
                .map(SeasonAuthorizationResultBo::getAppId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, AppletCustomizedContext> contextMap = appletUrlService.getCustomizedContextMap(appIds);
        List<MiniAppBaseInfoBo> miniAppBaseInfoBos = miniAppService.getMiniAppBaseInfoList(appIds, null);
        Map<String, MiniAppBaseInfoBo> appBaseInfoBoMap = miniAppBaseInfoBos.stream().collect(Collectors.toMap(MiniAppBaseInfoBo::getAppId, Function.identity(), FunctionUtil.override()));
        // 1. 查询指定mid所有的剧集id
        List<Long> allSeasonIds = fetchSeasonIdsByQueryParam(queryParam, appIdAuthorizationMapping, appletPriority);
        if (CollectionUtils.isEmpty(allSeasonIds)) {
            log.info("[AppletPlatformService] getAppletSeasonList no seasons found for mid: {}", mid);
            return PageResult.emptyPageResult();
        }
        // 2. 查询剧集所绑定的小程序id
        Map<Long, String> appletBySeasonIds = appletSeasonService.getDefaultAppletBySeasonIds(allSeasonIds);
        List<AppletSeasonUrlParam.SeasonEpIds> seasonEpIds = fetchSeasonEpIds(allSeasonIds);
        List<AppletSeasonUrl> appletSeasonUrls = appletUrlService.getBatchAppletSeasonUrls(
                seasonEpIds.stream()
                        .map(seasonEpId -> AppletSeasonUrlParam.builder()
                                .appletBaseInfos(miniAppBaseInfoBos)
                                .sourceFrom(null)
                                .seasonEpId(seasonEpId)
                                .build())
                        .collect(Collectors.toList()));
        Map<String, AppletSeasonUrl> seasonUrlMap = appletSeasonUrls.stream().filter(seasonUrl -> NumberUtil.isPositive(seasonUrl.getSeasonId()))
                .collect(Collectors.toMap(appletSeasonUrl -> AppletSeasonUrl.buildSeasonKey(appletSeasonUrl.getAppId(), appletSeasonUrl.getSeasonId()), Function.identity(), FunctionUtil.override()));
        // 2. 过滤不符合条件的剧集id
        filterIfNotMatch(queryParam.getAppId(), allSeasonIds, appBaseInfoBoMap, appletBySeasonIds, seasonUrlMap, appIdAuthorizationMapping.get(mid), appletPriority);
        // 3. 获取seasonBo信息
        Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(allSeasonIds)
                .appendEpisode(false)
                .appendVideoDimension(false)
                .appendAuthorDetail(false)
                .build());
        if (MapUtils.isEmpty(seasonBoMap)) {
            log.warn("[AppletPlatformService] fetchSeasonsByMid failed, no seasons found for seasonIds: {}", allSeasonIds);
            return PageResult.emptyPageResult();
        }
        // 4. 内存分页
        Page page = queryParam.getPage();
        List<SeasonBo> seasonBos = PageUtil.getSubListWithWindow(Lists.newArrayList(seasonBoMap.values()), page.getPage(), page.getPageSize());
        if (CollectionUtils.isEmpty(seasonBos)) {
            log.info("[AppletPlatformService] getAppletSeasonList no seasons found for mid: {}", mid);
            return PageResult.emptyPageResult();
        }
        // 5. 组装结果
        return new PageResult<>(allSeasonIds.size(), seasonBos.stream().map(season -> {
            String appId = appletBySeasonIds.get(season.getSeasonId());
            MiniAppBaseInfoBo defaultMiniAppBaseInfoBo;
            AppletSeasonBo appletSeasonBo = new AppletSeasonBo();
            List<SeasonAuthorizationResultBo> authResults = appIdAuthorizationMapping.getOrDefault(mid, Collections.emptyList());
            SeasonAuthorizationResultBo defaultAuthorization = seasonAuthorizationExtService.getDefaultAuthorization(authResults, appletPriority);
            if (StringUtils.isBlank(appId)) {
                appId = defaultAuthorization.getAppId();
            }
            defaultMiniAppBaseInfoBo = appBaseInfoBoMap.get(appId);
            AppletSeasonInfoBo seasonBo = seasonBoToAppletSeasonInfoBo(season, defaultMiniAppBaseInfoBo, seasonUrlMap);
            appletSeasonBo.setSeasonBo(seasonBo);
            String finalAppId = appId;
            appletSeasonBo.setCandidateApplets(authResults.stream().map(auth -> {
                AppletSeasonBo.CandidateApplet candidateApplet = new AppletSeasonBo.CandidateApplet();
                MiniAppBaseInfoBo candidate = appBaseInfoBoMap.get(auth.getAppId());
                if (candidate == null || Objects.isNull(contextMap.get(auth.getAppId()))) {
                    log.warn("[AppletPlatformService] getAppletSeasonList no miniAppBaseInfo found for appId: {}", auth.getAppId());
                    return null; // 没有小程序信息，跳过
                }
                candidateApplet.setCandidate(candidate);
                candidateApplet.setDefaultTag(StringUtils.equals(auth.getAppId(), finalAppId));
                return candidateApplet;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
            return appletSeasonBo;
        }).collect(Collectors.toList()));
    }

    private void filterIfNotMatch(String filterAppId,
                                  List<Long> allSeasonIds,
                                  Map<String, MiniAppBaseInfoBo> appBaseInfoBoMap,
                                  Map<Long, String> appletBySeasonIds,
                                  Map<String, AppletSeasonUrl> seasonUrlMap,
                                  List<SeasonAuthorizationResultBo> authResults,
                                  List<String> appletPriority) {
        allSeasonIds.removeIf(seasonId -> {
            String appId = appletBySeasonIds.get(seasonId);
            SeasonAuthorizationResultBo authorizationResultBo = seasonAuthorizationExtService.getDefaultAuthorization(authResults, appletPriority);
            // 没有小程序信息
            if (StringUtils.isBlank(appId)) {
                appId = authorizationResultBo.getAppId();
            }
            if (StringUtils.isNotBlank(filterAppId)) {
                return !StringUtils.equals(appId, filterAppId); // 如果指定了appId，则只保留匹配的剧集
            }
            MiniAppBaseInfoBo defaultMiniAppBaseInfoBo = appBaseInfoBoMap.get(appId);
            if (Objects.isNull(defaultMiniAppBaseInfoBo)) {
                log.warn("[AppletPlatformService] filterIfNotMatch no miniAppBaseInfo found for appId: {}", appId);
                return true; // 没有小程序信息，过滤掉
            }
            AppletSeasonUrl appletSeasonUrl = seasonUrlMap.get(AppletSeasonUrl.buildSeasonKey(appId, seasonId));
            if (appletSeasonUrl == null || !NumberUtil.isPositive(appletSeasonUrl.getSeasonId())) {
                log.warn("[AppletPlatformService] filterIfNotMatch no seasonUrl found for appId: {}, seasonId: {}", appId, seasonId);
                return true; // 没有小程序绑定的剧集，过滤掉
            }
            return false;
        });
    }

    private static List<AppletSeasonUrlParam.SeasonEpIds> fetchSeasonEpIds(List<Long> seasonIds) {
        List<AppletSeasonUrlParam.SeasonEpIds> seasonEpIds = new ArrayList<>();
        for (Long seasonId : seasonIds) {
            AppletSeasonUrlParam.SeasonEpIds seasonEpId = new AppletSeasonUrlParam.SeasonEpIds();
            seasonEpId.setSeasonId(seasonId);
            seasonEpIds.add(seasonEpId);
        }
        return seasonEpIds;
    }

    private List<Long> fetchSeasonIdsByQueryParam(AuthorSeasonQueryParam queryParam, Map<Long, List<SeasonAuthorizationResultBo>> appIdAuthorizationMapping, List<String> appletPriority) {
        Long seasonId = queryParam.getSeasonId();
        if (NumberUtil.isPositive(seasonId)) {
            // 如果指定了seasonId，则查询单个剧集
            return Lists.newArrayList(seasonId);
        }
        List<Pair<Long, String>> seasonIdCreateTimePairs = ogvSeasonService.getAllSeasonBoForSingleMid(queryParam.getMid(), null);
        return seasonIdCreateTimePairs.stream().map(Pair::getLeft).collect(Collectors.toList());
    }

    private List<Long> fetchSeasonIdsWithAppId(AuthorSeasonQueryParam queryParam,
                                               List<Long> allSeasonIds,
                                               Map<Long, List<SeasonAuthorizationResultBo>> appIdAuthorizationMapping,
                                               List<String> appletPriority) {
        // 通过appId查询绑定的剧集信息
        List<Long> seasonIds = appletSeasonService.getSeasonIdsByAppId(queryParam.getAppId());
        List<SeasonAuthorizationResultBo> resultBos = appIdAuthorizationMapping.get(queryParam.getMid());
        if (CollectionUtils.isEmpty(resultBos)) {
            log.info("[AppletPlatformService] fetchSeasonsByMid no authorization found for mid: {}", queryParam.getMid());
            return List.of();
        }
        SeasonAuthorizationResultBo defaultAuthorization = seasonAuthorizationExtService.getDefaultAuthorization(resultBos, appletPriority);
        if (defaultAuthorization == null || !StringUtils.equals(defaultAuthorization.getAppId(), queryParam.getAppId())) {
            log.warn("[AppletPlatformService] fetchSeasonsByMid no default tag found for mid: {}", queryParam.getMid());
            allSeasonIds = seasonIds;
        }
        return allSeasonIds;
    }

    @Override
    public AppletSeasonInfoBo getAppletSeasonInfo(String appId, Long seasonId, Long episodeId, Integer sourceFrom) throws Exception {
        AssertUtil.hasText(appId, ErrorCodeType.BAD_PARAMETER.getCode(), "appId不能为空");
        Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder().seasonIdList(List.of(seasonId)).build());
        SeasonBo season = seasonBoMap.get(seasonId);
        if (season == null) {
            log.error("[AppletPlatformService] getAppletInfo failed, season not found");
            return null;
        }
        UserInfoBo author = season.getAuthor();
        if (author == null || !authorAuthorizationService.isAuthorized(appId, author.getMid())) {
            log.error("[AppletPlatformService] getAppletInfo failed, season not authorized");
            throw new ServiceException(ErrorCodeType.UNAUTHORIZED.getCode(), "剧集未授权");
        }
        MiniAppBaseInfoBo miniAppBaseInfoBo = miniAppService.getMiniAppBaseInfo(appId, null, false);
        AppletSeasonUrl seasonUrl = appletUrlService.getSeasonUrl(miniAppBaseInfoBo, new AppletSeasonUrlParam.SeasonEpIds(seasonId, List.of(episodeId)), sourceFrom);
        if (seasonUrl == null) {
            log.error("[AppletPlatformService] getAppletSeasonInfo failed, seasonUrl not found for appId: {}, seasonId: {}, episodeId: {}", appId, seasonId, episodeId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集链接不存在");
        }
        AppletSeasonInfoBo.AppletSeasonInfoBoBuilder builder = AppletSeasonInfoBo.builder()
                .miniAppBaseInfo(miniAppBaseInfoBo)
                .seasonId(season.getSeasonId())
                .cover(season.getCover())
                .linkUrl(seasonUrl.getSeasonUrl())
                .seasonPaymentStatus(season.getPaymentStatus())
                .title(season.getTitle());
        if (NumberUtil.isPositive(episodeId)) {
            // 如果指定了episodeId，则获取对应的剧集信息
            EpisodeBo episodeBo = ogvSeasonService.queryEpisodeById(episodeId);
            if (episodeBo == null || !episodeBo.getSeasonId().equals(seasonId)) {
                log.error("[AppletPlatformService] getAppletSeasonInfo failed, episode not found for appId: {}, seasonId: {}, episodeId: {}", appId, seasonId, episodeId);
                throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集不存在");
            }
            builder.episodes(List.of(AppletEpisodeInfoBo.builder()
                    .episodeId(episodeBo.getEpisodeId())
                    .title(episodeBo.getTitle())
                    .cover(episodeBo.getCover())
                    .longTitle(episodeBo.getLongTitle())
                    .linkUrl(seasonUrl.getEpisodeUrlMap().get(episodeBo.getEpisodeId()))
                    .build()));
        }
        return builder.build();
    }

    @Override
    public void bindAppletSeason(String appId, Long seasonId) {
        AssertUtil.hasText(appId, ErrorCodeType.BAD_PARAMETER.getCode(), "appId不能为空");
        validateIfAppletExistsAndPathValid(List.of(appId));
        appletSeasonService.bindSeasonToApplet(seasonId, appId);
    }

    private void validateIfAppletExistsAndPathValid(List<String> appIds) {
        // 判断小程序是否有效
        List<MiniAppBaseInfoBo> miniAppBaseInfoBos = miniAppService.getMiniAppBaseInfoList(appIds, null);
        Map<String, MiniAppBaseInfoBo> baseInfoBoMap = miniAppBaseInfoBos.stream().collect(Collectors.toMap(MiniAppBaseInfoBo::getAppId, Function.identity(), FunctionUtil.override()));
        Map<String, AppletCustomizedContext> contextMap = appletUrlService.getCustomizedContextMap(appIds);
        List<String> invalidAppIds = appIds.stream()
                .filter(appId -> Objects.isNull(baseInfoBoMap.get(appId)) || Objects.isNull(contextMap.get(appId)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invalidAppIds)) {
            log.error("[AppletPlatformService] bindAppletSeason failed, invalid appIds: {}", invalidAppIds);
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(), "小程序信息不存在或路径配置无效: " + String.join(", ", invalidAppIds));
        }
    }

    @Override
    public List<AppletPriorityBo> getAppletPriority() {
        List<String> appletPriority = appletSeasonService.getAppletPriority();
        if (CollectionUtils.isEmpty(appletPriority)) {
            log.info("[AppletPlatformService] getAppletPriority no applet priority found, returning empty list");
            return List.of();
        }
        List<AppletPriorityBo> result = new ArrayList<>();
        List<MiniAppBaseInfoBo> miniAppBaseInfoList = miniAppService.getMiniAppBaseInfoList(appletPriority, null);
        Map<String, MiniAppBaseInfoBo> appBaseInfoBoMap = miniAppBaseInfoList.stream().collect(Collectors.toMap(MiniAppBaseInfoBo::getAppId, Function.identity(), FunctionUtil.override()));
        for (int i = 0; i < appletPriority.size(); i++) {
            String appId = appletPriority.get(i);
            MiniAppBaseInfoBo miniAppBaseInfoBo = appBaseInfoBoMap.get(appId);
            if (miniAppBaseInfoBo == null) {
                log.warn("[AppletPlatformService] getAppletPriority no miniAppBaseInfo found for appId: {}", appId);
                continue; // 没有小程序信息，跳过
            }
            result.add(AppletPriorityBo.builder().appletBaseInfo(miniAppBaseInfoBo).priority(i + 1).build());
        }
        return result;
    }

    @Override
    public void saveAppletPriority(List<String> appIdList) {
        validateIfAppletExistsAndPathValid(appIdList);
        appletSeasonService.saveAppletPriority(appIdList);
    }

    @Override
    public List<UserInfoBo> getAllAuthor() {
        List<Long> mids = seasonAuthorizationExtService.getAllAuthorizedMids();
        if (CollectionUtils.isEmpty(mids)) {
            log.info("[AppletPlatformService] getAllAuthor no authorized mids found");
            return List.of();
        }
        Map<Long, UserInfoBo> userInfoBoMap = mainSiteAccountServiceGrpcClient.queryUser(mids, GrpcCallContext.builder().timeout(2000).build());
        return userInfoBoMap.values().stream()
                .filter(userInfo -> userInfo != null && NumberUtil.isPositive(userInfo.getMid()))
                .collect(Collectors.toList());
    }

    private AppletSeasonInfoBo seasonBoToAppletSeasonInfoBo(SeasonBo seasonBo, MiniAppBaseInfoBo miniAppBaseInfoBo, Map<String, AppletSeasonUrl> seasonUrlMap) {
        String appId = miniAppBaseInfoBo.getAppId();
        AppletSeasonUrl appletSeasonUrl = seasonUrlMap.get(AppletSeasonUrl.buildSeasonKey(appId, seasonBo.getSeasonId()));
        AppletSeasonInfoBo.AppletSeasonInfoBoBuilder builder = AppletSeasonInfoBo.builder()
                .seasonId(seasonBo.getSeasonId())
                .title(seasonBo.getTitle())
                .linkUrl(Objects.nonNull(appletSeasonUrl) ? appletSeasonUrl.getSeasonUrl() : null)
                .seasonPaymentStatus(seasonBo.getPaymentStatus())
                .miniAppBaseInfo(miniAppBaseInfoBo)
                .cover(seasonBo.getCover());
        List<SectionBo> sections = seasonBo.getSections();
        if (Objects.nonNull(appletSeasonUrl)) {
            Map<Long, String> episodeUrlMap = appletSeasonUrl.getEpisodeUrlMap();
            if (CollectionUtils.isNotEmpty(sections)) {
                List<AppletEpisodeInfoBo> episodes = sections.stream()
                        .filter(section -> CollectionUtils.isNotEmpty(section.getEpisodes()))
                        .flatMap(section -> section.getEpisodes().stream())
                        .map(episode -> AppletEpisodeInfoBo.builder()
                                .episodeId(episode.getEpisodeId())
                                .title(episode.getTitle())
                                .cover(episode.getCover())
                                .longTitle(episode.getLongTitle())
                                .linkUrl(episodeUrlMap.getOrDefault(episode.getEpisodeId(), null))
                                .build())
                        .collect(Collectors.toList());
                builder.episodes(episodes);
            }
        }
        return builder.build();
    }
}
