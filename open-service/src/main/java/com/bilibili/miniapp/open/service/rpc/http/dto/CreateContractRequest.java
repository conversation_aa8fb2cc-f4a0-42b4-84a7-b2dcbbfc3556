package com.bilibili.miniapp.open.service.rpc.http.dto;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 创建合同请求
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class CreateContractRequest {

    /**
     * 签约业务（必填）
     */
    @NotNull
    private String business;

    /**
     * 签约主体列表：UP主、供应商等（必填）
     */
    @NotNull
    private List<String> entity;

    /**
     * 合同类型：个人、企业、个人&企业等（必填）
     * 1 - 个人
     * 2 - 企业
     * 3 - 个人 & 企业
     */
    @NotNull
    private Integer contractType;

    /**
     * 签约方列表（必填）
     */
    @NotNull
    private List<ContractEntityDto> entities;

    /**
     * OA审核信息（必填）
     */
    @NotNull
    private OaInfo oaInfo;

    /**
     * 必填模板ID（必填）
     */
    @NotNull
    private String requiredTplId;

    /**
     * 附件列表
     */
    private List<ContractAttachmentDto> attachments;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 合同发起方
     * 1. 甲方
     * 2. 乙方
     */
    private Long initiator;

    /**
     * 代填类型
     * 0 - 全部类型(查询认领列表使用)
     * 1 - 无代填
     * 2 - 业务方代填
     * 3 - 重签代填
     */
    private Long fillType;

    /**
     * 签约形式
     * 1 - 线上
     * 2 - 线下
     */
    private Long signModus;

    /**
     * 选填模板ID列表
     */
    private List<String> optionalTplIds;

    /**
     * 是否在OA归档
     * 1 - 是
     * 2 - 否
     */
    private Long isInOa;

    /**
     * 模板合同类型
     * 1 - 原合同(普通合同)
     * 2 - 补充协议(须关联合同流程)
     * 3 - 终止协议(须关联合同流程)
     */
    private Long tplContractType;

    /**
     * c端发起的创建人uid
     */
    private Long cCreator;

    /**
     * 签约时限，天数
     */
    private Long signTimeLimit;

    /**
     * 服务提供商
     * 1 - e签宝
     * 2 - docusign
     */
    private Long thirdSignPlatform;
}
