package com.bilibili.miniapp.open.service.biz.settlement.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.BFSKey;
import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.SettlementUtil;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.SettlementIdGenerator;
import com.bilibili.miniapp.open.common.util.SnowFlakeIdGenerator;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSettlementDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.accrual.IAccrualService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.IAccrualSettleMappingService;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams;
import com.bilibili.miniapp.open.service.bo.accrual.AccrualBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSettlementDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.SettlementConfig;
import com.bilibili.miniapp.open.service.rpc.http.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 结算服务实现
 * 金额单位为分保留2位小数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class SettlementService implements ISettlementService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private MiniAppOpenSettlementDao settlementDao;

    @Autowired
    private IFinanceService financeService;

    @Autowired
    private IBFSService bfsService;

    @Autowired
    private HuilianyiPaymentService huilianyiPaymentService;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private IAccrualService accrualService;

    @Autowired
    private IContractService contractService;

    @Autowired
    private IAccrualSettleMappingService accrualSettleMappingService;

    @Autowired
    private ICacheRepository redis;


    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final String PAYMENT_PREFIX = "MINI_APP_PAY_";

    /**
     * 固定比例：(1+6%)，用作计算金额
     */
    private final BigDecimal fixedProportionRatio = new BigDecimal("0.06").add(BigDecimal.ONE);

    @Override
    public SettlementDateListBo getSettlementDates(Long mid, String appId) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        List<AccrualBo> accrualList = accrualService.queryAccruals(appId, WithdrawStatus.READY_TO_WITHDRAW);

        List<SettlementDateBo> settlementList = accrualList.stream()
                .map(bo -> SettlementDateBo.builder()
                        .date(bo.getIncomeDate())
                        .accrualId(bo.getAccrualId())
                        .build())
                .collect(Collectors.toList());

        return SettlementDateListBo.builder()
                .settlementList(settlementList)
                .build();
    }

    @Override
    public SettlementPreviewBo getSettlementPreview(Long mid, String appId, List<String> accrualIds) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        if (CollectionUtils.isEmpty(accrualIds)) {
            return SettlementPreviewBo.emptyInstance();
        }

        List<AccrualBo> accrualList = accrualService.queryAccruals(appId, accrualIds);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");

        Long withdrawApplyAmount = accrualList.stream()
                .map(AccrualBo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);

        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null, ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        Long taxFee = calculateTaxFee(withdrawApplyAmount, taxType.getTaxRatio());

        Long actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxType.getTaxRatio());

        return SettlementPreviewBo.builder()
                .withdrawApplyAmount(withdrawApplyAmount)
                .taxFee(taxFee)
                .actualWithdrawAmount(actualWithdrawAmount)
                .build();
    }

    /**
     * 由于可支持的文件类型比较多，需要解析文件类型映射到http media type的逻辑会比较复杂，直接使用bfs url返回的类型
     */
    @Override
    public InvoiceUploadBo uploadInvoice(long mid, String settlementId, MultipartFile file) {
        AssertUtil.isTrue(accountService.isCompanyOwner(mid), ErrorCodeType.NO_PERMISSION);

        BFSUploadResult bfsUploadResult = uploadBfs(file);

        AssertUtil.isTrue(StringUtils.isNotBlank(bfsUploadResult.getUrl()), ErrorCodeType.NO_DATA.getCode(), "上传后未返回图片url");

        String partName = getBfsFileNameFromUrl(bfsUploadResult.getUrl());

        HuilianyiUploadResult huiLianYiResult = huilianyiPaymentService.uploadAttachments(bfsUploadResult.getUrl(), partName);

        checkInvoice(mid, settlementId, bfsUploadResult.getUrl());

        return InvoiceUploadBo.builder()
                .oid(huiLianYiResult.getOid())
                .url(bfsUploadResult.getUrl())
                .build();

    }

    private BFSUploadResult uploadBfs(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes();
            return bfsService.upload(BFSKey.CATEGORY_MINIAPP_OPEN, file.getOriginalFilename(), fileBytes);
        } catch (Exception e) {
            log.error("发票上传失败", e);
            throw new ServiceException("发票上传失败: " + e.getMessage());
        }
    }

    /**
     * <a href="https://opendocs.huilianyi.com/implement/business-data/receipt/receipt-ocr-open.html?h=ocr">ocr接口</a>
     */
    private void checkInvoice(long mid, String settlementId, String invoiceUrl) {

        HuilianyiOcrResult ocrResult = ocrInvoice(invoiceUrl);
        HuilianyiOcrResult.OcrResult invoice = ocrResult.getData().getIdentify_results().get(0);
        AssertUtil.isTrue(isVATSpecialElectronicInvoice(invoice), ErrorCodeType.BAD_PARAMETER.getCode(), "发票类型错误，应为增值税电子专用发票");

        HuilianyiOcrResult.InvoiceDetails details = invoice.getDetails();

        String invoiceBuyer = details.getBuyer();
        String biliCompanyName = configCenter.getSettlementConfig().getCompanyName();
        AssertUtil.isTrue(Objects.equals(invoiceBuyer, biliCompanyName), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票购买方名称【{}】与公司名称【{}】不一致", invoiceBuyer, biliCompanyName));

        String invoiceBuyerTaxId = details.getBuyerTaxId();
        String taxpayerIdentificationNumber = configCenter.getSettlementConfig().getTaxpayerIdentificationNumber();
        AssertUtil.isTrue(Objects.equals(invoiceBuyerTaxId, taxpayerIdentificationNumber), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票购买方纳税人识别号【{}】与公司识别号【{}】不一致", invoiceBuyerTaxId, taxpayerIdentificationNumber));

        CompanyDetailBo company = companyService.getDetail(mid);
        String invoiceSeller = details.getSeller();
        String companyName = company.getCompanyInfo().getCompanyName();
        AssertUtil.isTrue(Objects.equals(invoiceSeller, companyName), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票销售方名称【{}】与公司名称【{}】不一致", invoiceSeller, companyName));

        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null, ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");
        String invoiceSellerTaxId = details.getSellerTaxId();
        String creditCode = company.getCompanyInfo().getCreditCode();
        AssertUtil.isTrue(Objects.equals(creditCode, invoiceSellerTaxId), ErrorCodeType.NO_DATA.getCode(), StrUtil.format("发票销售方纳税人识别号【{}】与公司识别号【{}】不一致", invoiceSellerTaxId, creditCode));

        List<HuilianyiOcrResult.InvoiceItem> items = details.getItems();
        AssertUtil.isTrue(!CollectionUtils.isEmpty(items), ErrorCodeType.NO_DATA.getCode(), "发票项目名称不存在");
        //只有一个发票项目
        HuilianyiOcrResult.InvoiceItem item = items.get(0);
        InvoiceItemCategory invoiceItem = InvoiceItemCategory.getByCode(financeDetail.getInvoiceInfo().getInvoiceItemCategory());
        AssertUtil.isTrue(Objects.equals(item.getName(), invoiceItem.getDesc()), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票项目名称【{}】与财务信息中配置的项目名称【{}】不同", item.getName(), invoiceItem.getDesc()));

        MiniAppOpenSettlementPo settlement = getSettlement(settlementId);
        AssertUtil.isTrue(settlement != null, ErrorCodeType.NO_DATA.getCode(), "结算单不存在");
        String invoiceTotal = details.getTotal();
        String actualWithdrawAmount = SettlementUtil.convertCentToYuan(settlement.getActualWithdrawAmount());
        AssertUtil.isTrue(Objects.equals(invoiceTotal, actualWithdrawAmount), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票金额【{}】与结算单实际结算金额【{}】不一致", invoiceTotal, actualWithdrawAmount));

        String invoiceTaxRate = item.getTaxRate();
        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getInvoiceType());
        String taxRatio = SettlementUtil.toPercentage(taxType.getTaxRatio());
        AssertUtil.isTrue(Objects.equals(invoiceTaxRate, taxRatio), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票税率【{}】与财务信息中配置的税率【{}】不一致", invoiceTaxRate, taxRatio));

        String invoiceTax = item.getTax();
        String taxFee = SettlementUtil.convertCentToYuan(settlement.getTaxFee());
        AssertUtil.isTrue(Objects.equals(invoiceTax, taxFee), ErrorCodeType.BAD_DATA.getCode(), StrUtil.format("发票税额【{}】与结算单税费【{}】不一致", invoiceTax, taxFee));
    }


    private boolean isVATSpecialElectronicInvoice(HuilianyiOcrResult.OcrResult invoice) {
        String typeCode = "10100";
        String invoiceTypeNo = "08";
        return Objects.equals(invoice.getType(), typeCode)
                && Objects.equals(invoice.getDetails().getElectronicMark(), "1")
                && Objects.equals(invoice.getDetails().getInvoiceTypeNo(), invoiceTypeNo);
    }

    private HuilianyiOcrResult ocrInvoice(String invoiceUrl) {
        String key = StrUtil.format(RedisKeyPattern.INVOICE_OCR.getPattern());

        HuilianyiOcrResult result = redis.getObject(key, HuilianyiOcrResult.class);
        if (result != null) {
            return result;
        }
        result = huilianyiPaymentService.receiptOcr(invoiceUrl);
        redis.setObject(key, result, 3, TimeUnit.MINUTES);
        return result;
    }

    @Override
    public PageResult<SettlementItemBo> getSettlementList(Long mid,
                                                          String appId,
                                                          Integer page,
                                                          Integer size,
                                                          Long beginTime,
                                                          Long endTime,
                                                          Integer settlementStatus) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        MiniAppOpenSettlementPoExample.Criteria criteria = example.createCriteria();

        criteria.andAppIdEqualTo(appId);

        if (beginTime != null) {
            criteria.andSettlementBeginTimeGreaterThanOrEqualTo(new Timestamp(beginTime));
        }

        if (endTime != null) {
            criteria.andSettlementEndTimeLessThanOrEqualTo(new Timestamp(endTime));
        }

        if (settlementStatus != null) {
            criteria.andSettlementStatusEqualTo(settlementStatus);
        }

        criteria.andIsDeletedEqualTo(0);

        long total = settlementDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        Page pageInfo = Page.valueOf(page, size);
        example.setLimit(pageInfo.getLimit());
        example.setOffset(pageInfo.getOffset());
        example.setOrderByClause("id desc");

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);


        List<SettlementItemBo> settlementItemList = settlementList.stream()
                .map(this::convertToSettlementItemBo)
                .collect(Collectors.toList());

        return new PageResult<>((int) total, settlementItemList);
    }

    @Override
    public SettlementDetailBo getSettlementDetail(Long mid, String settlementId) {
        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(settlementList), ErrorCodeType.NO_DATA.getCode(), "结算单不存在");

        MiniAppOpenSettlementPo settlement = settlementList.get(0);

        AssertUtil.isTrue(accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        CompanyDetailBo detail = companyService.getDetail(mid);

        return SettlementDetailBo.builder()
                .settlementId(settlement.getSettlementId())
                .settlementBeginTime(settlement.getSettlementBeginTime())
                .settlementEndTime(settlement.getSettlementEndTime())
                .companyName(detail.getCompanyInfo().getCompanyName())
                .actualWithdrawAmount(settlement.getActualWithdrawAmount())
                .settlementStatus(settlement.getSettlementStatus())
                .paymentOrderId(settlement.getPaymentOrderId())
                .build();
    }

    private SettlementItemBo convertToSettlementItemBo(MiniAppOpenSettlementPo po) {
        return SettlementItemBo.builder()
                .settlementId(po.getSettlementId())
                .appId(po.getAppId())
                .settlementBeginTime(po.getSettlementBeginTime())
                .settlementEndTime(po.getSettlementEndTime())
                .settlementStatus(po.getSettlementStatus())
                .withdrawApplyAmount(po.getWithdrawApplyAmount())
                .actualWithdrawAmount(po.getActualWithdrawAmount())
                .taxFee(po.getTaxFee())
                .build();
    }

    private String getBfsFileNameFromUrl(String url) {
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url).build();
        List<String> pathSegments = uriComponents.getPathSegments();
        return pathSegments.get(pathSegments.size() - 1);
    }

    /**
     * 计算税费：withdraw_apply_amount/(1+6%)*税率，进行四舍五入，过程保留4位
     */
    private Long calculateTaxFee(Long withdrawApplyAmount, String taxRatio) {
        BigDecimal amount = new BigDecimal(withdrawApplyAmount);
        return amount
                .multiply(new BigDecimal(taxRatio))
                .divide(fixedProportionRatio, 0, RoundingMode.HALF_UP)
                .longValue();
    }

    /**
     * 计算实际提现金额：withdraw_apply_amount/(1+6%)*(1+税率)，进行四舍五入，过程保留4位
     */
    private Long calculateActualWithdrawAmount(Long withdrawApplyAmount, String taxRatio) {
        BigDecimal amount = new BigDecimal(withdrawApplyAmount);
        return amount
                .multiply(BigDecimal.ONE.add(new BigDecimal(taxRatio)))
                .divide(fixedProportionRatio, 4, RoundingMode.HALF_UP)
                .longValue();
    }

    @Override
    public void confirmSettlement(Long mid, String settlementId) {
        ((SettlementService) AopContext.currentProxy()).updateSettlementStatus(mid, settlementId, SettlementStatus.PENDING_UPLOAD_INVOICE, SettlementStatus.PENDING_CONFIRMATION);
    }

    @Override
    public void cancelSettlement(Long mid, String settlementId) {
        ((SettlementService) AopContext.currentProxy()).updateSettlementStatus(mid, settlementId, SettlementStatus.PENDING_UPLOAD_INVOICE, SettlementStatus.CANCELED);
    }

    private void updateSettlementStatus(String paymentOrderId, SettlementStatus targetStatus) {
        MiniAppOpenSettlementPo po = getSettlementByPayment(paymentOrderId);
        if (po == null) {
            return;
        }
        ((SettlementService) AopContext.currentProxy()).updateSettlementStatus(null, po.getSettlementId(), null, targetStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementStatus(Long mid, String settlementId, SettlementStatus sourceStatus, SettlementStatus targetStatus) {
        MiniAppOpenSettlementPo settlement = getSettlement(settlementId);
        AssertUtil.isTrue(settlement != null, ErrorCodeType.NO_DATA.getCode(), "结算单不存在");

        AssertUtil.isTrue(mid == null || accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);
        AssertUtil.isTrue(sourceStatus == null || settlement.getSettlementStatus() == sourceStatus.getCode(), ErrorCodeType.NO_DATA.getCode(), "结算单状态不正确");

        MiniAppOpenSettlementPoExample updateExample = new MiniAppOpenSettlementPoExample();
        MiniAppOpenSettlementPoExample.Criteria criteria = updateExample.createCriteria()
                .andSettlementIdEqualTo(settlementId)

                .andIsDeletedEqualTo(0);

        if (sourceStatus != null) {
            criteria.andSettlementStatusEqualTo(sourceStatus.getCode());
        }

        MiniAppOpenSettlementPo updateRecord = new MiniAppOpenSettlementPo();
        updateRecord.setSettlementStatus(targetStatus.getCode());

        settlementDao.updateByExampleSelective(updateRecord, updateExample);

        List<String> accrualIds = accrualSettleMappingService.getAccrualIds(settlementId);
        accrualService.updateStatus(settlement.getAppId(), accrualIds, WithdrawStatus.getBySettlementStatus(targetStatus));
    }

    private MiniAppOpenSettlementPo getSettlementByPayment(String paymentOrderId) {

        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andPaymentOrderIdEqualTo(paymentOrderId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        if (CollectionUtils.isEmpty(settlementList)) {
            return null;
        }
        return settlementList.get(0);
    }

    private MiniAppOpenSettlementPo getSettlement(String settlementId) {

        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        if (CollectionUtils.isEmpty(settlementList)) {
            return null;
        }
        return settlementList.get(0);
    }

    @Override
    @LockRequest(key = "'create_settlement_'+#request.getAccrualIds()")
    public void createSettlement(Long mid, SettlementCreateBo request) {

        AssertUtil.isTrue(!CollectionUtils.isEmpty(request.getAccrualIds()), ErrorCodeType.BAD_PARAMETER.getCode(), "预提单ID列表不能为空");

        String appId = request.getAppId();
        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);
        ContractSettlementDetailBo contractDetail = contractService.getContractDetail(mid, request.getAppId(), false);
        AssertUtil.isTrue(contractDetail.getContractStatus() == ContractStatusEnum.EFFECTIVE.getCode(), ErrorCodeType.BAD_DATA.getCode(), "合同未生效");

        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null && financeDetail.getInvoiceInfo() != null, ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        List<AccrualBo> accrualList = accrualService.queryAccruals(request.getAppId(), request.getAccrualIds());
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");
        AssertUtil.isTrue(accrualList.size() == request.getAccrualIds().size(), ErrorCodeType.BAD_PARAMETER.getCode(), "部分预提单不存在");

        boolean allWithdrawable = accrualList.stream().allMatch(accrual -> Objects.equals(accrual.getWithdrawStatus(), WithdrawStatus.READY_TO_WITHDRAW.getCode()));
        AssertUtil.isTrue(allWithdrawable, ErrorCodeType.BAD_DATA.getCode(), "所有预提单必须处于可提现状态");

        validateAccrualDatesContinuity(accrualList);

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        ((SettlementService) AopContext.currentProxy()).createSettlementRecord(appId, accrualList, taxType);
    }


    @Override
    public InvoiceIssuanceBaseInfoBo getInvoiceBaseInfo() {
        SettlementConfig settlementConfig = configCenter.getSettlementConfig();
        return InvoiceIssuanceBaseInfoBo.builder()
                .bankCode(settlementConfig.getBankCode())
                .bankName(settlementConfig.getBankName())
                .companyName(settlementConfig.getCompanyName())
                .taxpayerIdentificationNumber(settlementConfig.getTaxpayerIdentificationNumber())
                .build();
    }

    /**
     * 校验预提单时间连续性
     */
    private void validateAccrualDatesContinuity(List<AccrualBo> accrualList) {
        List<String> sortedDates = accrualList.stream()
                .map(AccrualBo::getIncomeDate)
                .sorted()
                .collect(Collectors.toList());

        for (int i = 1; i < sortedDates.size(); i++) {
            String prevDate = sortedDates.get(i - 1);
            String currDate = sortedDates.get(i);

            try {
                LocalDate prevLocalDate = LocalDate.parse(prevDate, FORMATTER);
                LocalDate currLocalDate = LocalDate.parse(currDate, FORMATTER);

                if (!currLocalDate.equals(prevLocalDate.plusDays(1))) {
                    throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(),
                            String.format("预提单日期不连续，%s 和 %s 之间有间隔", prevDate, currDate));
                }
            } catch (DateTimeParseException e) {
                throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "预提单日期格式错误");
            }
        }
    }

    /**
     * 创建结算单记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void createSettlementRecord(String appId, List<AccrualBo> accrualList, TaxType taxType) {

        List<String> sortedDates = accrualList.stream()
                .map(AccrualBo::getIncomeDate)
                .sorted()
                .collect(Collectors.toList());

        Timestamp settlementBeginTime = parseIncomeDate(sortedDates.get(0));
        Timestamp settlementEndTime = parseIncomeDate(sortedDates.get(sortedDates.size() - 1));

        String settlementId = generateSettlementId();

        Long withdrawApplyAmount = accrualList.stream()
                .map(AccrualBo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);

        String taxRatio = taxType.getTaxRatio();
        Long taxFee = calculateTaxFee(withdrawApplyAmount, taxRatio);
        Long actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxRatio);

        MiniAppOpenSettlementPo settlementPo = MiniAppOpenSettlementPo.builder()
                .appId(appId)
                .settlementBeginTime(settlementBeginTime)
                .settlementEndTime(settlementEndTime)
                .settlementId(settlementId)
                .settlementStatus(SettlementStatus.PENDING_CONFIRMATION.getCode())
                .withdrawApplyAmount(withdrawApplyAmount)
                .actualWithdrawAmount(actualWithdrawAmount)
                .taxFee(taxFee)
                .extra(JSON.toJSONString(SettlementExtraBo.builder()
                        .taxRatio(taxRatio)
                        .build()))
                .build();

        settlementDao.insertSelective(settlementPo);

        List<String> accrualIds = accrualList.stream()
                .map(AccrualBo::getAccrualId)
                .collect(Collectors.toList());

        accrualSettleMappingService.recordMapping(settlementId, accrualIds);

        accrualService.updateStatus(appId, accrualIds, WithdrawStatus.WITHDRAWING);
    }

    /**
     * 解析收入日期字符串为Timestamp
     */
    private Timestamp parseIncomeDate(String incomeDate) {
        try {
            LocalDate date = LocalDate.parse(incomeDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            return Timestamp.valueOf(date.atStartOfDay());
        } catch (DateTimeParseException e) {
            throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "日期格式错误: " + incomeDate);
        }
    }

    /**
     * 生成结算单ID
     */
    private String generateSettlementId() {
        return "BL" + SettlementIdGenerator.nextId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LockRequest(key = "'initiate_settlement_order'+#request.getSettlementId()")
    public void initiateSettlementOrder(Long mid, SettlementOrderInitiateBo request) {

        MiniAppOpenSettlementPo settlement = getSettlement(request.getSettlementId());
        AssertUtil.isTrue(settlement != null, ErrorCodeType.NO_DATA.getCode(), "结算单不存在");

        String appId = settlement.getAppId();

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        AssertUtil.isTrue(settlement.getSettlementStatus().equals(SettlementStatus.PENDING_UPLOAD_INVOICE.getCode()), ErrorCodeType.BAD_DATA.getCode(), "结算单已发生变化，请重新操作");

        ExpenseCreateMandatoryParams expenseParams = buildExpenseCreateParams(mid, settlement, request);
        HuilianyiExpenseCreateResult expenseResult = huilianyiPaymentService.createExpense(expenseParams);

        if (expenseResult.isSuccess()) {
            log.warn("【发起结算】结算单[{}]生成付款单失败，参数:{}，返回:{}", settlement.getSettlementId(), JSON.toJSONString(expenseParams), JSON.toJSONString(expenseResult));
        }
        AssertUtil.isTrue(expenseResult.isSuccess(), ErrorCodeType.BAD_REQUEST.getCode(), expenseResult.getMessage());

        String paymentOrderId = expenseResult.getKey();
        MiniAppOpenSettlementPo updateRecord = new MiniAppOpenSettlementPo();
        updateRecord.setInvoiceOid(request.getInvoiceOid());
        updateRecord.setInvoiceUrl(request.getInvoiceUrl());
        updateRecord.setPaymentOrderId(paymentOrderId);
        updateRecord.setSettlementStatus(SettlementStatus.PENDING_AUDIT.getCode());

        MiniAppOpenSettlementPoExample updateExample = new MiniAppOpenSettlementPoExample();
        updateExample.createCriteria()
                .andSettlementIdEqualTo(request.getSettlementId())
                .andIsDeletedEqualTo(0);

        settlementDao.updateByExampleSelective(updateRecord, updateExample);
    }

    @Override
    public void processCallBack(List<HuilianyiExpenseCallback> callbacks) {
        callbacks.stream()
                .filter(callback -> StringUtils.isNotBlank(callback.getBusinessCode()))
                .filter(callback -> callback.getBusinessCode().startsWith(PAYMENT_PREFIX))
                .forEach(callback -> updateSettlementStatus(callback.getBusinessCode(), SettlementStatus.getByPaymentStatus(callback.getExpenseStatus())));
    }

    /**
     * 构建汇联易提现单参数
     */
    private ExpenseCreateMandatoryParams buildExpenseCreateParams(long mid, MiniAppOpenSettlementPo settlement, SettlementOrderInitiateBo request) {
        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);

        CompanyDetailBo company = companyService.getCreatedCompanyDetail(mid);
        ContractSettlementDetailBo contractDetail = contractService.getContractDetail(mid, settlement.getAppId(), false);
        String appId = settlement.getAppId();
        List<String> accrualIds = accrualSettleMappingService.getAccrualIds(settlement.getSettlementId());

        List<AccrualBo> accrualBos = accrualService.queryAccruals(appId, accrualIds);
        HuilianyiOcrResult ocrResult = ocrInvoice(request.getInvoiceUrl());
        HuilianyiInvoiceWrapper invoiceWrapper = HuilianyiInvoiceWrapper.builder()
                .uploadOid(request.getInvoiceOid())
                .ocrResult(ocrResult)
                .build();
        ExpenseCreateMandatoryParams.SimpleOcrResult simpleOcrResult = ExpenseCreateMandatoryParams.SimpleOcrResult.fromInvoiceWrapper(invoiceWrapper);

        return new ExpenseCreateMandatoryParams()
                .setInvoiceImgUrls(List.of(request.getInvoiceUrl()))
                .setPayee(company.getCompanyInfo().getCreditCode())
                .setContractNumber(contractDetail.getContractId())
                .setBankNumber(financeDetail.getBankInfo().getBankAccountNumber())
                .setBusinessCode(PAYMENT_PREFIX + SnowFlakeIdGenerator.nextId())
                .setOcrResults(Map.of(request.getInvoiceUrl(), simpleOcrResult))
                .setWithdrawAmtInCny(new BigDecimal(SettlementUtil.convertCentToYuan(settlement.getActualWithdrawAmount())))
                .setAccrualId2AmtInCny(accrualBos.stream()
                        .collect(Collectors.toMap(AccrualBo::getAccrualId,
                                accrual -> SettlementUtil.convertCentToYuanBigDecimal(accrual.getTotalAmount()))));

    }
}
