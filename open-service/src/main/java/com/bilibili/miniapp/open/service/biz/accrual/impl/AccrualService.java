package com.bilibili.miniapp.open.service.biz.accrual.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.SnowFlakeIdGenerator;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAccrualDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.accrual.IAccrualService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.biz.income.impl.IncomeService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.bo.accrual.AccrualBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyInfoBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.mapper.AccrualMapper;
import com.bilibili.miniapp.open.service.rpc.http.dto.SupplierResultDto;
import com.bilibili.miniapp.open.service.rpc.http.impl.ContractCenterApiService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/6
 */
@Slf4j
@Service
public class AccrualService implements IAccrualService {

    @Autowired
    private MiniAppOpenAccrualDao accrualDao;
    @Autowired
    private HuilianyiPaymentService huilianyiPaymentService;
    @Autowired
    private IFinanceService financeService;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;
    @Autowired
    private IncomeService incomeService;
    @Autowired
    private ContractCenterApiService contractCenterApiService;

    private static final String ACCRUAL_PREFIX = "MINI_APP_ACC_";

    @Override
    public List<AccrualBo> queryAccruals(String appId, WithdrawStatus withdrawStatus) {
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andWithdrawStatusEqualTo(withdrawStatus.getCode())
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualPo> accrualPos = accrualDao.selectByExample(example);
        return AccrualMapper.MAPPER.poListToBoList(accrualPos);
    }

    @Override
    public List<AccrualBo> queryAccruals(String appId, List<String> accrualIds) {
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAccrualIdIn(accrualIds)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualPo> accrualPos = accrualDao.selectByExample(example);
        return AccrualMapper.MAPPER.poListToBoList(accrualPos);
    }


    public String doCreateAccrual(FinanceDetailBo financeDetail, List<IncomeDetailBo> incomeDetailBos) {
        long totalAmount = incomeDetailBos.stream()
                .map(IncomeDetailBo::getActualIncomeAmount)
                .reduce(0L, Long::sum);

        IncomeDetailBo anyOne = incomeDetailBos.get(0);
        CompanyInfoBo company = financeDetail.getCompany();
        SupplierResultDto supplierInfo = contractCenterApiService.getSupplierInfo(company.getCompanyName());
        if (supplierInfo == null || !supplierInfo.inUse()) {
            String msg = StrUtil.format("公司[{}][{}],供应商信息不可用:{}", company.getCompanyName(), company.getCompanyId(), JSON.toJSONString(supplierInfo));
            throw new ServiceException(msg);
        }

        String incomeDate = anyOne.getIncomeDate();
        String yyyyMM = incomeDate.substring(0, 6);
        AccrualCreateMandatoryParams query = new AccrualCreateMandatoryParams()
                .setBusinessCode(ACCRUAL_PREFIX + SnowFlakeIdGenerator.nextId())
                .setPayee(supplierInfo.getResp().getId())
                .setAmtInCny(BigDecimal.valueOf(totalAmount))
                .setPeriod(yyyyMM);

        HuilianyiAccrualCreateResult result = huilianyiPaymentService.createAccrual(query);
        String successCode = "0000";
        AssertUtil.isTrue(Objects.equals(successCode, result.getErrorCode()), ErrorCodeType.BAD_DATA.getCode(), "创建预提单失败");
        return result.getKey();
    }

    @Override
    @LockRequest(key = "'create_accrual'+#incomeDate")
    public void createAccrual(String incomeDate) {
        List<IncomeDetailBo> incomeDetailBos = incomeService.queryIncomeDetails(incomeDate);
        if (CollectionUtils.isEmpty(incomeDetailBos)) {
            return;
        }
        ThreadPoolExecutor pool = ThreadPoolUtil.getExecutor(ThreadPoolType.SETTLEMENT);
        Map<String, List<IncomeDetailBo>> groupByAppId = incomeDetailBos.stream().collect(Collectors.groupingBy(IncomeDetailBo::getAppId));
        List<String> allAppIds = new ArrayList<>(groupByAppId.keySet());
        Lists.partition(allAppIds, pool.getCorePoolSize())
                .forEach(partitionAppIds ->
                        pool.execute(() -> {

                            Map<String, List<IncomeDetailBo>> partitionMap = partitionAppIds.stream().collect(Collectors.toMap(appId -> appId, groupByAppId::get));

                            partitionMap.forEach((appId, sameAppIdIncomeBos) -> {
                                ChannelMiniAppInfoDTO appInfo = miniAppRemoteService.queryAppInfoWithinCache(appId);
                                FinanceDetailBo financeDetail = financeService.getFinanceDetail(appInfo.getMid());
                                if (financeDetail == null) {
                                    log.info("[创建预提单]小程序[{}]没有财务信息，跳过生成预提单", appId);
                                    return;
                                }
                                Map<String, List<IncomeDetailBo>> groupByDate = sameAppIdIncomeBos.stream().collect(Collectors.groupingBy(IncomeDetailBo::getIncomeDate));
                                groupByDate.forEach((date, sameTimeIncomeBos) ->
                                        processAccrual(financeDetail, sameTimeIncomeBos));
                            });
                        })
                );
    }

    public void processAccrual(FinanceDetailBo financeDetail, List<IncomeDetailBo> incomeDetailBos) {
        incomeDetailBos.forEach(incomeService::fillAllAmountIfNecessary);
        try {
            String accrualId = doCreateAccrual(financeDetail, incomeDetailBos);
            incomeDetailBos.forEach(bo -> bo.setAccrualId(accrualId));
            incomeService.updateAfterAccrualCompleted(incomeDetailBos);
        } catch (Exception e) {
            log.warn("[创建预提单]创建预提单失败，incomeDetailBos: {}", incomeDetailBos, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String appId, List<String> accrualIds, WithdrawStatus targetStatus) {

        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAccrualIdIn(accrualIds)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualPo> accrualPos = accrualDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accrualPos)) {
            return;
        }

        MiniAppOpenAccrualPo updatePo = new MiniAppOpenAccrualPo();
        updatePo.setWithdrawStatus(targetStatus.getCode());
        accrualDao.updateByExampleSelective(updatePo, example);

        List<String> dates = accrualPos.stream()
                .map(MiniAppOpenAccrualPo::getIncomeDate)
                .distinct()
                .collect(Collectors.toList());
        incomeService.updateStatus(appId, dates, targetStatus);
    }

}
