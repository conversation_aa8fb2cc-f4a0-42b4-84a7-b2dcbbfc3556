package com.bilibili.miniapp.open.service.config;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
public class ContractConfig {

    /**
     * 模板合同id，需要在合同中心创建，联系产品
     */
    private String contractTemplateId = "TPL569890775559604334";

    /**
     * 配置更新不支持解析为map，不要配置，内容和contractTemplateId强关联
     * 参数映射字段来自
     *
     * @see com.bilibili.miniapp.open.service.bo.contract.FillContractBo
     */
    private Map<String, String> contractFormMap = new HashMap<>() {{
        put("乙方-基本信息-联系地址", "contactAddress");
        put("乙方-基本信息-联系人", "signatoryName");
        put("乙方-基本信息-手机", "signatoryPhone");
        put("乙方-基本信息-电子邮件", "signatoryEmail");
        put("乙方-基本信息-企业名称", "companyName");
        put("乙方-基本信息-小程序APPID", "appId");
        put("乙方-基本信息-小程序名称", "appName");
        put("起始年", "startYear");
        put("起始月", "startMonth");
        put("起始日", "startDay");
        put("签署年", "signYear");
        put("签署月", "signMonth");
        put("签署日", "signDay");
        put("结束年", "endYear");
        put("结束月", "endMonth");
        put("结束日", "endDay");
    }};

    private String createContractOperator = "zhangkaige01";
}
