package com.bilibili.miniapp.open.service.bo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票上传业务对象
 *
 * <AUTHOR>
 * @date 2025/5/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceUploadBo {

    /**
     * 汇联易上传后返回的oid
     */
    private String oid;

    /**
     * BFS上传后返回的url
     */
    private String url;
}
