package com.bilibili.miniapp.open.service.rpc.grpc.server;

import com.bapis.ad.applet.Pageable;
import com.bapis.ad.applet.RequestContext;
import com.bapis.ad.applet.ResponseContext;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.util.TraceUtil;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.google.protobuf.Message;
import com.google.protobuf.TextFormat;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.lang.reflect.Method;

@Slf4j
public abstract class AbstractGrpcService {

    // 通用处理器模板
    protected <REQ extends Message, RESP extends Message> void process(
            REQ request,
            StreamObserver<RESP> responseObserver,
            String method,
            MethodProcessor<REQ, RESP> businessProcessor,
            ErrorResponseComposer<RESP> errorResponseComposer
    ) {
        
        try {
            // 1. 提取请求上下文
            RequestContext context = extractContext(request);
            // 2. 前置检查
            preCheck(context, method);
            putTraceId(context);
            // 3. 权限校验
            validateAuth(context);
            // 4. 记录请求日志
            logRequest(request, method);
            // 5. 执行业务逻辑
            RESP businessResponse = businessProcessor.process(request, context);
            // 6. 构建成功响应
            businessResponse = injectSuccessContext(businessResponse);
            // 7. 记录完成日志
            logCompletion(businessResponse, method);
            responseObserver.onNext(businessResponse);
            responseObserver.onCompleted();
        } catch (Exception e) {
            handleException(e, responseObserver, errorResponseComposer, method);
        } finally {
            MDC.remove(TraceUtil.TRACE_ID);
        }
    }

    /**
     * 自动上下文提取规则（可被子类覆盖）
     */
    protected RequestContext extractContext(Message request) {
        try {
            Method getContext = request.getClass().getMethod("getCtx");
            return (RequestContext) getContext.invoke(request);
        } catch (Exception e) {
            throw new IllegalStateException("请求协议必须包含getCtx()方法", e);
        }
    }

    /**
     * 响应上下文注入逻辑（可基于AOP实现）
     */
    @SuppressWarnings("unchecked")
    protected <RESP extends Message> RESP injectSuccessContext(Message response) {
        try {
            // 反射获取构建器
            Method toBuilderMethod = response.getClass().getMethod("toBuilder");
            Message.Builder builder = (Message.Builder) toBuilderMethod.invoke(response);

            // 调用构建器的setCtx方法
            Method setContextMethod = builder.getClass().getMethod("setCtx", ResponseContext.class);
            setContextMethod.invoke(builder, buildSuccessContext());

            // 构建新对象
            return (RESP) builder.build();
        } catch (Exception e) {
            throw new IllegalStateException("协议对象必须包含上下文构建能力", e);
        }
    }

    private ResponseContext buildSuccessContext() {
        return ResponseContext.newBuilder()
                .setCode(0)
                .setMsg("success")
                .build();
    }
    
    protected Page convertToPage(Pageable pageable) {
        if (pageable == null) {
            return new Page();
        }
        int pageIndex = pageable.getPageIndex();
        int pageSize = pageable.getPageSize();
        if (pageIndex == 0) {
            pageIndex = Page.DEFAULT_PAGE;
        }
        if (pageSize == 0) {
            pageSize = Page.DEFAULT_LIMIT;
        }
        pageSize = Math.min(pageSize, Page.MAX_LIMIT); // 限制最大页大小
        return Page.valueOf(pageIndex, pageSize);
    }

    private static void putTraceId(RequestContext context) {
        String traceId = context.getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = TraceUtil.genTraceId();
        }
        MDC.put(TraceUtil.TRACE_ID, traceId);
    }

    //------------------------ 核心接口定义（支持不同方法定制） ------------------------
    @FunctionalInterface
    public interface MethodProcessor<REQ, RESP> {
        /**
         * 方法级业务处理策略（建议通过方法引用实现）
         * @param request 具体请求对象
         * @param context 公共上下文
         * @return 业务响应对象
         */
        RESP process(REQ request, RequestContext context) throws Exception;
    }

    @FunctionalInterface
    public interface ErrorResponseComposer<RESP> {
        /**
         * 错误响应构建器
         * @param e 异常信息
         * @return 错误响应对象
         */
        RESP compose(Throwable e);
    }

    //============ 可扩展的钩子方法 ============//
    protected void preCheck(RequestContext context, String method) {
        if (context.getCaller().isEmpty()) {
            log.error("[AbstractGrpcService] {} Missing required field: caller", method);
            throw new IllegalArgumentException("Missing required field: caller");
        }
    }

    protected ResponseContext defaultErrorResponseContext(Throwable e) {
        ResponseContext errorContext;
        if (e instanceof IllegalArgumentException) {
            errorContext = ResponseContext.newBuilder()
                    .setCode(-400)
                    .setMsg(e.getMessage()).build();
        } else {
            errorContext = ResponseContext.newBuilder()
                    .setCode(-500)
                    .setMsg("Internal server error").build();
        }
        return errorContext;
    }

    protected void validateAuth(RequestContext context) {
        // 默认鉴权逻辑，可被override
    }

    protected <REQ extends Message> void logRequest(REQ request, String method) {
        log.info("[AbstractGrpcService] {} request={}", method, request);
    }

    protected <RESP extends Message> void logCompletion(RESP response, String method) {
        log.info("[AbstractGrpcService] {} response={}", method, response);
    }

    //============ 异常处理 ============//
    private <RESP extends Message> void handleException(Exception e,
                                      StreamObserver<RESP> responseObserver,
                                      ErrorResponseComposer<RESP> processor,
                                        String method) {
        RESP compose = processor.compose(e);
        try {
            responseObserver.onNext(compose);
            responseObserver.onCompleted();
        } catch (Exception ex) {
            log.error("[AbstractGrpcService] {} Error sending error response", method, ex);
            responseObserver.onError(ex);
        }
        log.error("[AbstractGrpcService] {} gRPC processing error", method, e);
    }
}
