package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.cmd.miniapp.*;
import com.bilibili.mall.miniapp.dto.miniapp.*;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.mall.miniapp.query.miniapp.*;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.github.pagehelper.PageInfo;
import okhttp3.RequestBody;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/01/02 21:02
 */
@RESTClient(name = "miniapp-app", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppRemoteService {
    /**
     * 获取小程序列表
     *
     * @param miniAppQuery 查询结构体
     * @return BiliCall<Response < PageInfo < MiniAppDTO>>
     * @see MiniAppQuery
     */
    @POST(value = "/miniapp/service/listMiniAppDTOS")
    BiliCall<Response<PageInfo<MiniAppDTO>>> listMiniAppDTOS(@Body RequestBody miniAppQuery);


    /**
     * 根据appid列表获取小程序列表
     *
     */
    @GET(value = "/miniapp/service/listMiniApps")
    BiliCall<Response<List<MiniAppDTO>>> listMiniAppsByAppIds(@Query(value = "appIds") List<String> appIds);


    /**
     * 修改小程序最大邀请数
     *
     * @param appId      小程序id
     * @param maxUpCount 小程序最大邀请数
     * @return true成功 false失败
     */
    @GET(value = "/miniapp/service/updateMaxUpCount")
    BiliCall<Response<Boolean>> updateMaxUpCount(@Query(value = "appId") String appId,
                                                 @Query(value = "maxUpCount") Integer maxUpCount);

    /**
     * 修改主小程序的激励广告设置
     *
     * @param miniAppRewardedAdUpdateCmd 查询结构体
     * @return
     * @see MiniAppRewardedAdUpdateCmd
     */
    @POST(value = "/miniapp/service/rewardedAd/update")
    BiliCall<Response<Boolean>> updateMiniAppRewardedAd(@Body RequestBody miniAppRewardedAdUpdateCmd);

    /**
     * 创建主小程序
     *
     * @param miniAppAddQuery
     * @return 生成的小程序名称
     * @see MiniAppAddQuery
     */
    @POST(value = "/miniapp/service/company/miniapp")
    BiliCall<Response<String>> insertMiniApp(@Body RequestBody miniAppAddQuery);

    /**
     * 创建主小程序并返回
     *
     * @param miniAppAddQuery
     * @return 生成的小程序信息
     * @see MiniAppAddQuery
     */
    @POST(value = "/miniapp/service/company/miniapp/create")
    BiliCall<Response<MiniAppDTO>> createMiniApp(@Body RequestBody miniAppAddQuery);


    /**
     * 更新小程序支付能力
     *
     * @param miniAppPayUpdateQuery 更新结构体
     * @return 结果
     * @see MiniAppPayUpdateQuery
     */
    @POST(value = "/miniapp/service/pay")
    BiliCall<Response<Boolean>> updateMiniAppAllowPay(@Body RequestBody miniAppPayUpdateQuery);

    /**
     * 更新内部小程序支付能力
     *
     * @param miniAppPayUpdateQuery 更新结构体
     * @return 结果
     * @see MiniAppPayUpdateQuery
     */
    @POST(value = "/miniapp/service/internal/pay")
    BiliCall<Response<Boolean>> updateInternalMiniAppAllowPay(@Body RequestBody miniAppPayUpdateQuery);

    /**
     * 更新小程序虚拟支付能力
     *
     * @param miniAppPayUpdateQuery 更新结构体
     * @return 结果
     * @see MiniAppPayUpdateQuery
     */
    @POST(value = "/miniapp/service/virtual/pay")
    BiliCall<Response<Boolean>> updateMiniAppVirtualPay(@Body RequestBody miniAppPayUpdateQuery);

    /**
     * 查询小程序支付能力操作记录
     *
     * @param miniAppPayRecordQuery 查询结构体
     * @return 结果
     * @see MiniAppPayRecordQuery
     */
    @POST(value = "/miniapp/service/pay_record/list")
    BiliCall<Response<PageInfo<MiniAppPayRecordDTO>>> listMiniAppPayRecordDTOS(@Body RequestBody miniAppPayRecordQuery);

    /**
     * 修改小程序标签
     *
     * @param miniAppLabelListUpdateCmd 修改结构体
     * @return true成功 失败抛出异常
     * @see MiniAppLabelListUpdateCmd
     */
    @POST(value = "/miniapp/service/labelList/update")
    BiliCall<Response<Boolean>> updateLabelList(@Body RequestBody miniAppLabelListUpdateCmd);


    /**
     * 修改小游戏关注up主
     *
     * @param miniAppFollowUpUpdateCmd
     * @return
     * @see MiniAppFollowUpUpdateCmd
     */
    @POST(value = "/miniapp/service/follow_up/update")
    BiliCall<Response<Boolean>> updateMiniAppFollowUp(@Body RequestBody miniAppFollowUpUpdateCmd);

    /**
     * 小程序下架
     *
     * @param miniAppOfflineStatusUpdateCmd
     * @return
     * @see MiniAppOfflineStatusUpdateCmd
     */
    @POST(value = "/miniapp/service/offline")
    BiliCall<Response<Boolean>> updateMiniAppOfflineStatus(@Body RequestBody miniAppOfflineStatusUpdateCmd);

    /**
     * openId - mid 转换查询
     *
     * @param appId
     * @param mid
     * @param openId
     * @return
     */
    @GET(value = "/miniapp/service/exchange/info/get")
    BiliCall<Response<MiniAppExchangeInfoDTO>> getExchangeInfo(@Query(value = "appId") String appId,
                                                               @Query(value = "mid") Long mid,
                                                               @Query(value = "openId") String openId);

    /**
     * 更新小程序降级能力
     *
     * @param miniAppBackupStatusUpdateCmd
     * @return
     * @see MiniAppBackupStatusUpdateCmd
     */
    @POST(value = "/miniapp/service/backup/status/update")
    BiliCall<Response<Boolean>> updateMiniAppBackupStatus(@Body RequestBody miniAppBackupStatusUpdateCmd);

    /**
     * 修改主小程序降级页面url
     *
     * @param miniAppBackupUrlUpdateCmd
     * @return
     * @see MiniAppBackupUrlUpdateCmd
     */
    @POST(value = "/miniapp/service/backup/url/update")
    BiliCall<Response<Boolean>> updateMiniAppBackupUrl(@Body RequestBody miniAppBackupUrlUpdateCmd);


    /**
     * 获取小程序信息
     *
     * @param miniAppInfoQuery
     * @return
     * @see MiniAppInfoQuery
     */
    @POST(value = "/miniapp/service/info")
    BiliCall<Response<ChannelMiniAppInfoDTO>> getMiniAppInfoDTO(@Body RequestBody miniAppInfoQuery);

    /**
     * 获取MiniAppDTO
     *
     * @param appId  企业小程序id
     * @param vAppId up主关联小程序id
     * @return MiniAppDTO
     */
    @GET(value = "/miniapp/service/mini_app_dto")
    BiliCall<Response<MiniAppDTO>> getMiniAppDTO(@Query(value = "appId") String appId,
                                                 @Query(value = "vAppId") String vAppId);


    /**
     * 检查小程序名是否符合规则、是否包含敏感词和是否重复
     *
     * @param appId 小程序id
     * @param name  小程序名字
     * @return 检查通过true 否false
     */
    @GET(value = "/miniapp/service/name/valid")
    BiliCall<Response<Boolean>> validName(@Query(value = "appId") String appId, @Query(value = "name") String name);

    @GET(value = "/miniapp/service/name/check")
    BiliCall<Response<List<MiniAppCheckDTO>>> nameCheck(@Query(value = "name") String name);

    @GET(value = "/miniapp/service/name/repeat")
    BiliCall<Response<List<MiniAppCheckDTO>>> nameRepeat(@Query(value = "name") String name);

    @POST(value = "/miniapp/service/name/update/v2")
    BiliCall<Response<Boolean>> updateName(@Query(value = "appId") String appId, @Query(value = "name") String name);

    /**
     * 小程序基本信息配置
     *
     * @param  miniAppInfoUpdateQuery 小程序基本信息配置请求结构体
     * @return 成功true 失败false
     * @see MiniAppInfoUpdateQuery
     */
    @POST(value = "/miniapp/service/info/update")
    BiliCall<Response<ChannelMiniAppInfoDTO>> updateInfo(@Body RequestBody miniAppInfoUpdateQuery);

    @POST(value = "/miniapp/service/batch/info")
    BiliCall<Response<List<MiniAppBaseInfoDto>>> getMiniAppBaseInfos(@Body RequestBody miniAppQueryInfo);

    @POST(value = "/miniapp/service/batch/info/page")
    BiliCall<Response<PageResult<MiniAppBaseInfoDto>>> getMiniAppBaseInfosPage(@Body RequestBody queryParamDto);

}
