package com.bilibili.miniapp.open.service.biz.payment.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.entity.SignParameterEntity;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.util.OkHttpUtil;
import com.bilibili.miniapp.open.common.util.SignUtil;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.order.IOrderService;
import com.bilibili.miniapp.open.service.biz.payment.IPaymentService;
import com.bilibili.miniapp.open.service.biz.retry.OpenRetryCallable;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.bo.order.Refund;
import com.bilibili.miniapp.open.service.bo.payment.RefundNotifyDeveloperInfo;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 【退款回调开发者】重试执行器
 * <p>
 * 注意：将退款作为Payment Domain流程更清晰，不会产生Order和Payment循环依赖，这也是该OpenRetryCallable没放到order包下面的原因
 *
 * <AUTHOR>
 * @date 2025/1/24 17:05
 */
@Slf4j
@Component
public class RefundNotifyDeveloperRetryCallable implements OpenRetryCallable {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private IOpenAccessService accessService;

    @Override
    public RetryBizType bizType() {
        return RetryBizType.ORDER_REFUND_NOTIFY_DEVELOPER;
    }

    @Override
    public boolean call(OpenRetryContext ctx) {
        String ctxStr = JSON.toJSONString(ctx);
        log.info("[RefundNotifyDeveloperRetryCallable] prepare that notify refund info to developer, ctx={}", ctxStr);
        long orderId = ctx.getBizId();
        String devRefundId = ctx.getReqId();
        try {
            Order order = orderService.getOrder(orderId);
            if (Objects.isNull(order)) {
                //如果查不到订单，则直接置为成功
                log.info("[RefundNotifyDeveloperRetryCallable] give up that notify refund info to developer, because of NoOrder, ctx={}", ctxStr);
                return true;
            }

            Refund refund = paymentService.getRefund(orderId, devRefundId);

            RefundStatus refundStatus = RefundStatus.getByCodeWithoutEx(refund.getRefundStatus());
            NotifyStatus notifyStatus = NotifyStatus.getByCodeWithoutEx(refund.getNotifyStatus());

            //回调条件：
            // 1、传了回调地址
            // 2、退款成功/失败但未回调开发者时(失败或者未回调)
            boolean needNotify = StringUtils.hasText(refund.getNotifyUrl())
                    && (Objects.equals(refundStatus, RefundStatus.SUCCESS) || Objects.equals(refundStatus, RefundStatus.FAILED))
                    && !Objects.equals(notifyStatus, NotifyStatus.SUCCESS);
            if (!needNotify) {
                log.info("[RefundNotifyDeveloperRetryCallable] give up that notify refund info to developer, because of NoNeedNotify, ctx={}", ctxStr);
                return true;
            }

            RefundNotifyDeveloperInfo refundNotifyDeveloperInfo = RefundNotifyDeveloperInfo.builder()
                    .order_id(Objects.toString(order.getOrderId()))
                    .dev_order_id(order.getDevOrderId())
                    .dev_refund_id(refund.getDevRefundId())
                    .refund_amount(refund.getRefundAmount())
                    .refund_status(refundStatus.getCode())
                    .refund_time(refund.getRefundTime().getTime())
                    .extra_data(Objects.nonNull(refund.getExtra()) ? refund.getExtra().getDevExtraData() : null)
                    .build();

            SignParameterEntity signParameterEntity = SignParameterEntity.builder()
                    .param2(refundNotifyDeveloperInfo)
                    .ts(System.currentTimeMillis())
                    .build();

            OpenAccessBo access = accessService.getAccess(order.getAccessKey());
            String sign = SignUtil.sign(signParameterEntity, access.getAccessToken());

            String notifyUrl = String.format("%s?ts=%d&sign=%s", refund.getNotifyUrl(), signParameterEntity.getTs(), sign);
            String body = JSON.toJSONString(refundNotifyDeveloperInfo);
            log.info("[RefundNotifyDeveloperRetryCallable] start that notify refund info to developer, notifyUrl={}, body={}",
                    notifyUrl, body);
            Response<?> response;
            try {

                response = OkHttpUtil.bodyPost(notifyUrl)
                        .json(JSON.toJSONString(refundNotifyDeveloperInfo))
                        .callForObjectUnsafe(Response.class);
            } catch (Exception e) {
                log.warn("[RefundNotifyDeveloperRetryCallable]退款回调 调用开发者回调地址[{}]异常:", notifyUrl, e);
                throw e;
            }

            log.info("[RefundNotifyDeveloperRetryCallable] end that notify refund info to developer, notifyUrl={}, body={}, response={}",
                    notifyUrl, body, JSON.toJSONString(response));

            boolean notifySuccessful = Objects.equals(response.getCode(), ErrorCodeType.SUCCESS.getCode());
            NotifyStatus curNotifyStatus = notifySuccessful ? NotifyStatus.SUCCESS : NotifyStatus.FAILED;

            //如果本次回调的状态没有发生变化，则无需更新订单，防止无效的变更消息
            if (!Objects.equals(curNotifyStatus, notifyStatus)) {
                Refund updateRefund = Refund.builder()
                        .id(refund.getId())
                        .orderId(refund.getOrderId())
                        .devRefundId(refund.getDevRefundId())
                        .notifyStatus(notifySuccessful ? NotifyStatus.SUCCESS.getCode() : NotifyStatus.FAILED.getCode())
                        .build();
                paymentService.updateRefund(updateRefund);
            }
            return notifySuccessful;
        } catch (Exception e) {
            log.error("[RefundNotifyDeveloperRetryCallable] process refund notify to developer error,ctx={}", ctxStr, e);
        }
        return false;
    }
}
