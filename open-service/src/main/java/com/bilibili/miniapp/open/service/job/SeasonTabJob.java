package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.ogv.ISeasonTabService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
@Component
@JobHandler("SeasonTabJob")
public class SeasonTabJob extends AbstractJobHandler{

    @Autowired
    private ISeasonTabService seasonTabService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        seasonTabService.cacheAllSeasonTab();
        return ReturnT.SUCCESS;
    }
}
