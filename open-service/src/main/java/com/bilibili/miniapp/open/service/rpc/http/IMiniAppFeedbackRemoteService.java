package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppFeedbackDTO;
import com.bilibili.miniapp.open.common.entity.Response;
import com.github.pagehelper.PageInfo;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/11
 **/

@RESTClient(name = "miniapp-feedback", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppFeedbackRemoteService {

    /**
     * 获取小程序反馈列表
     * @param miniAppFeedbackQuery
     * @return
     */
    @POST(value = "/miniapp/feedback/service/query")
    BiliCall<Response<PageInfo<MiniAppFeedbackDTO>>> getMiniAppFeedbackDTOS(@Body RequestBody miniAppFeedbackQuery);
}
