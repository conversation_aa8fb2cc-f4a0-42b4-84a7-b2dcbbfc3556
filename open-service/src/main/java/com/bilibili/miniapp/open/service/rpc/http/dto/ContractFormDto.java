package com.bilibili.miniapp.open.service.rpc.http.dto;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class ContractFormDto {

    private ContractFormContractDto contract;

    private List<ContractApplyTemplateDto> contractApplyTemplates;
}
