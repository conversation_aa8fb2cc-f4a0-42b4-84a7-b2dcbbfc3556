package com.bilibili.miniapp.open.service.rpc.http.dto;

import lombok.Data;

/**
 * <a href="http://bapi.bilibili.co/project/7653/interface/api/616366">保存相对方基础信息-三方接口（新）</a>
 *
 * <AUTHOR>
 * @date 2025/6/16
 */
@Data
public class SupplierBasicInfoDto {

    /**
     * 相对方类型，0-公司，1-个人
     * 必填
     */
    private Integer supplierType;

    /**
     * 相对方名称
     * 必填
     */
    private String supplierName;

    /**
     * 企业类别（企业地域），0-中国大陆；1-港澳台；2-海外
     * 非必填，为公司时必填
     */
    private Integer enterpriseRegion;

    /**
     * 统一社会信用代码/注册登记编码
     * 非必填，为公司时必填
     */
    private String comCreditCode;

    /**
     * 工商注册号
     * 非必填，为公司时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，非必填
     */
    private String bizRegistrationNo;

    /**
     * 企业性质，0-大陆企业，1-社会组织，3-中国香港公司，4-事业单位，5-中国台湾公司，6-基金会，7-医院，8-海外公司，9-律师事务所，10-学校，11-机关单位，-1-其他
     * 非必填，为公司时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，默认为8，不可修改
     */
    private Integer enterpriseType;

    /**
     * 注册资本
     * 非必填，为公司时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，非必填
     */
    private String registCapital;

    /**
     * 企业类型
     * 非必填
     */
    private String enterpriseKind;

    /**
     * 经营范围
     * 非必填，为公司时必填
     */
    private String scope;

    /**
     * 成立日期，格式为 yyyy-MM-dd
     * 非必填，为公司时必填
     */
    private String startDate;

    /**
     * 吊销日期，格式为 yyyy-MM-dd
     * 非必填，为公司时非必填
     */
    private String endDate;

    /**
     * 登记状态
     * 非必填
     */
    private String registrationStatus;

    /**
     * 法定代表人名称
     * 非必填，为公司时必填
     */
    private String legalPersonName;

    /**
     * 更新日期，格式 yyyy-MM-dd HH:mm:ss
     * 非必填，为公司时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，非必填
     */
    private String enterpriseUpdateDate;

    /**
     * 注册地址
     * 非必填，为公司时必填
     */
    private String address;

    /**
     * 营业期限始，格式 yyyy-MM-dd
     * 非必填，为公司时必填
     */
    private String businessTermStart;

    /**
     * 营业期限止，格式 yyyy-MM-dd
     * 非必填，为公司时必填
     */
    private String businessTermEnd;

    /**
     * 所属国家/地区
     * 非必填，公司/个人时都必填
     */
    private String nation;

    /**
     * 省份
     * 非必填，公司/个人时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，非必填
     */
    private String province;

    /**
     * 城市
     * 非必填，公司/个人时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，非必填
     */
    private String city;

    /**
     * 区域
     * 非必填，为公司时有值
     * 当企业类别为【0-中国大陆】、【1-港澳台】时，必填；当企业类别为【2-海外】，非必填
     */
    private String district;

    /**
     * 办公地址
     * 非必填，为公司时有值
     */
    private String officeAddress;

    /**
     * 备注
     * 非必填
     */
    private String remark;

    //非公司内容注释
    ///**
    // * 证件类型，0-居民身份证；1-港澳居民来往内地通行证；2-台湾居民来往大陆通行证；3-港澳居民居住证；4-台湾居民居住证；5-外国护照；6-外国人永久居留身份证；7-其他
    // * 非必填，为个人时必填
    // */
    //private Integer idCardType;
    //
    ///**
    // * 证件号码
    // * 非必填，为个人时必填
    // */
    //private String idCardNo;
    //
    ///**
    // * 证件有效期起，格式 yyyy-MM-dd
    // * 非必填，为个人时必填
    // */
    //private String idStartDate;
    //
    ///**
    // * 证件有效期止，格式 yyyy-MM-dd
    // * 非必填，为个人时必填
    // */
    //private String idEndDate;
    //
    ///**
    // * 性别，0-男；1-女
    // * 非必填，为个人时必填
    // */
    //private Integer sexual;
    //
    ///**
    // * 出生年月日，格式 yyyy-MM-dd
    // * 非必填，为个人时必填
    // */
    //private String birthday;
    //
    ///**
    // * 是否成年，0-否；1-是
    // * 非必填，为个人时必填
    // */
    //private Integer orAudit;
}
