package com.bilibili.miniapp.open.service.biz.ogv.impl;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonVideoViewSummaryDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonVideoViewSummaryPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonVideoViewSummaryPoExample;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonVideoViewSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Service
public class SeasonVideoViewSummaryService implements ISeasonVideoViewSummaryService {

    @Autowired
    private MiniAppOpenSeasonVideoViewSummaryDao seasonVideoViewDao;

    @Override
    public PageResult<String> queryHotSeasonIds(Long excludeSeasonId, Page page) {

        MiniAppOpenSeasonVideoViewSummaryPoExample example = new MiniAppOpenSeasonVideoViewSummaryPoExample();
        example.or()
                .andSeasonIdNotEqualTo(excludeSeasonId)
                .andIsDeletedEqualTo(0);
        long count = seasonVideoViewDao.countByExample(example);
        if (count == 0) {
            return PageResult.emptyPageResult();
        }

        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        example.setOrderByClause("video_view desc");

        List<MiniAppOpenSeasonVideoViewSummaryPo> pos = seasonVideoViewDao.selectByExample(example);
        return PageResult.<String>builder()
                .total(Math.toIntExact(count))
                .records(pos.stream()
                        .map(MiniAppOpenSeasonVideoViewSummaryPo::getSeasonId)
                        .map(String::valueOf)
                        .collect(Collectors.toList()))
                .build();
    }
}
