package com.bilibili.miniapp.open.service.bo.applet;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppletEpisodeInfoBo {
    private Long episodeId; // 集id
    private String title; // 集标题
    private String cover; // 集封面
    private String linkUrl; // 集跳转链接
    private String longTitle; // 集标题（长）
}
