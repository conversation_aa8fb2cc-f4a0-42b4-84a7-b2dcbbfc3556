package com.bilibili.miniapp.open.service.biz.feedback.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppFeedbackDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppFeedbackQuery;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.biz.feedback.IMiniAppFeedbackService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppFeedbackRemoteService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/11
 **/

@Service
@Slf4j
public class MiniAppFeedbackService implements IMiniAppFeedbackService {

    @Resource
    private MiniAppFeedbackRemoteService miniAppFeedbackRemoteService;

    @Override
    public PageResult<MiniAppFeedbackDTO> getMiniAppFeedbackDTOS(MiniAppFeedbackQuery miniAppFeedbackQuery) {
        PageInfo<MiniAppFeedbackDTO> miniAppFeedbackDTOS = miniAppFeedbackRemoteService.getMiniAppFeedbackDTOS(miniAppFeedbackQuery);
        if (miniAppFeedbackDTOS == null || CollectionUtils.isEmpty(miniAppFeedbackDTOS.getList())) {
            log.warn("getMiniAppFeedbackDTOS returned null for query: {}", miniAppFeedbackQuery);
            return new PageResult<>();
        }
        return new PageResult<>(Math.toIntExact(miniAppFeedbackDTOS.getTotal()), miniAppFeedbackDTOS.getList());
    }
}
