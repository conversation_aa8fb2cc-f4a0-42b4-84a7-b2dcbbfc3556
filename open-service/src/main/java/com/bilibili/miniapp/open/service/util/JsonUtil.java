package com.bilibili.miniapp.open.service.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.vavr.control.Try;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/13
 */
@Deprecated
public class JsonUtil {

    private static ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);


    public static <T> T fromJson(Object valueMap, Class<T> clazz) {
        return objectMapper.convertValue(valueMap, clazz);
    }


    public static <T> T readValue(String value, Class<T> clazz) {
        return Try.of(() -> {
            return objectMapper.readValue(value, clazz);
        }).getOrNull();
    }

    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) {
        return Try.of(() -> {
            return objectMapper.readValue(content, valueTypeRef);
        }).getOrNull();
    }

    public static String writeValueAsString(Object value) {
        return Try.of(() -> {
            return objectMapper.writeValueAsString(value);
        }).getOrNull();
    }
}
