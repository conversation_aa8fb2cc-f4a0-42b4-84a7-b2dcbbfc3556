package com.bilibili.miniapp.open.service.rpc.grpc.server;

import com.bapis.ad.applet.platform.company.*;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.mapper.CompanyBizMapper;
import com.bilibili.miniapp.open.service.mapper.MiniAppBizMapper;
import com.bilibili.miniapp.open.service.rpc.grpc.server.delegate.AppletCompanyGrpcServiceDelegate;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27
 **/

@RPCService
@Slf4j
public class AppletCompanyGrpcService extends AppletEnterpriseServiceGrpc.AppletEnterpriseServiceImplBase {

    @Resource
    private AppletCompanyGrpcServiceDelegate companyGrpcServiceDelegate;

    @Override
    public void getAppletEnterpriseInfo(GetAppletEnterpriseInfoRequest request, StreamObserver<GetAppletEnterpriseInfoResponse> responseObserver) {
        companyGrpcServiceDelegate.getAppletEnterpriseInfo(request, responseObserver);
    }

    @Override
    public void getAppletEnterpriseApps(GetAppletEnterpriseAppsRequest request, StreamObserver<GetAppletEnterpriseAppsResponse> responseObserver) {
        companyGrpcServiceDelegate.getAppletEnterpriseApps(request, responseObserver);
    }
}
