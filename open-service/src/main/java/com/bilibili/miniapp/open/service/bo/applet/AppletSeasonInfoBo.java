package com.bilibili.miniapp.open.service.bo.applet;

import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppletSeasonInfoBo {
    private MiniAppBaseInfoBo miniAppBaseInfo; // 小程序基础信息
    private Long seasonId; // 剧集id
    private String title; // 剧集标题
    private String cover; // 剧集封面
    private String linkUrl; // 剧集跳转链接
    private List<AppletEpisodeInfoBo> episodes; // 剧集列表
}
