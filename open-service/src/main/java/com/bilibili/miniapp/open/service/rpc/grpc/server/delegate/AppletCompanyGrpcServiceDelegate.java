package com.bilibili.miniapp.open.service.rpc.grpc.server.delegate;

import com.bapis.ad.applet.platform.company.*;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.mapper.CompanyBizMapper;
import com.bilibili.miniapp.open.service.mapper.MiniAppBizMapper;
import com.bilibili.miniapp.open.service.rpc.grpc.server.AbstractGrpcService;
import io.grpc.stub.StreamObserver;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Service
public class AppletCompanyGrpcServiceDelegate extends AbstractGrpcService {

    @Resource
    private ICompanyService companyService;

    @Resource
    private IMiniAppService miniAppService;

    public void getAppletEnterpriseInfo(GetAppletEnterpriseInfoRequest request, StreamObserver<GetAppletEnterpriseInfoResponse> responseObserver) {
        process(
                request, //请求
                responseObserver, //响应
                "getAppletEnterpriseInfo", //方法名
                (req, context) -> doGetAppletEnterpriseInfo(req.getMid()), // 实际业务逻辑处理
                e -> GetAppletEnterpriseInfoResponse.newBuilder()
                        .setCtx(defaultErrorResponseContext(e))
                        .build()
        );
    }

    private GetAppletEnterpriseInfoResponse doGetAppletEnterpriseInfo(Long mid) {
        CompanyDetailBo companyDetail = companyService.getCreatedCompanyDetail(mid);
        if (companyDetail == null || companyDetail.getCompanyInfo() == null) {
            return GetAppletEnterpriseInfoResponse.newBuilder().build();
        }
        return GetAppletEnterpriseInfoResponse.newBuilder()
                .setEnterpriseInfo(CompanyBizMapper.MAPPER.toEnterpriseInfo(companyDetail.getCompanyInfo()))
                .build();
    }

    public void getAppletEnterpriseApps(GetAppletEnterpriseAppsRequest request, StreamObserver<GetAppletEnterpriseAppsResponse> responseObserver) {
        process(
                request, //请求
                responseObserver, //响应
                "getAppletEnterpriseApps", //方法名
                (req, context) -> doGetAppletEnterpriseApps(req.getGetAppletEnterpriseAppsParam()), // 实际业务逻辑处理
                e -> GetAppletEnterpriseAppsResponse.newBuilder()
                        .setCtx(defaultErrorResponseContext(e))
                        .build()
        );
    }

    private GetAppletEnterpriseAppsResponse doGetAppletEnterpriseApps(GetAppletEnterpriseAppsParam param) {
        List<MiniAppBaseInfoBo> miniAppBaseInfoBos = miniAppService.queryAvailableApps(param.getMidList(), param.getAppId(), param.getAppName(), param.getSourceFrom().getValue());
        return GetAppletEnterpriseAppsResponse.newBuilder()
                .addAllAppList(
                        miniAppBaseInfoBos.stream()
                                .map(MiniAppBizMapper.MAPPER::toAppletBaseInfo)
                                .filter(Objects::nonNull)
                                .collect(java.util.stream.Collectors.toList())
                ).build();
    }

}
