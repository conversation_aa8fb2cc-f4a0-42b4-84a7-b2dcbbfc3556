package com.bilibili.miniapp.open.service.biz.ogv;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.client.season.ClientUserSeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.PlatformSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/27 15:33
 */
public interface IOgvSeasonService {
    /**
     * 查询剧集信息
     * PS：短剧场景
     */
    Map<Long, SeasonBo> querySeason4Short(SeasonQueryBo seasonQuery) throws Exception;

    Map<Long, Boolean> queryEpisodesVisibilityByAids(List<Long> aids);

    List<EpisodeBo> queryEpisodeByAids(List<Long> aids);

    PageResult<PlatformSeasonInfoBo> querySeasons(String appId, long upMid, Page page);

    PageResult<SeasonBo> queryTabSeasonsForPlatform(String appId, int tabType, Page page);

    PageResult<SeasonBo> queryTabSeasonsForClient(String appId, int tabType, Page page);

    ClientUserSeasonBo queryUserSeason(String appId, String openId, long seasonId);

    void addSeasonToTab(String appId, int tabType, List<Long> seasonIdList);

    PageResult<SeasonBo> searchSeasons(Long mid, Page page, String seasonName, boolean appendEpisode);

    EpisodeBo queryEpisodeById(Long episodeId);

    PageResult<SeasonBo> searchSeasons(String appId, Page page, String seasonName, boolean appendEpisode);

    List<Pair<Long, String>> getAllSeasonBoForSingleMid(Long mid, String seasonName);
}
