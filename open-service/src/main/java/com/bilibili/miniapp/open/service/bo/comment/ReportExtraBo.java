package com.bilibili.miniapp.open.service.bo.comment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 上报数据对象
 * 除了track_id以外，其余参数都放在reportExtra中，由小程序客户端上报到北极星
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
public class ReportExtraBo {

    /**
     * 跳转方式(评论蓝链=10000)
     * 由于兼容性原因，暂时保留，后期会废弃，使用sourcefrom
     */
    @Deprecated
    @JSONField(name = "jump_type")
    private String jumpType;


    /**
     * 二创视频avid
     */
    @JSONField(name = "from_avid")
    private String fromAvid;

    /**
     * 是否为作者评论
     */
    @JSONField(name = "is_up_reply")
    private String isUpReply;

    @JSONField(name = "track_id")
    private String trackId;
}
