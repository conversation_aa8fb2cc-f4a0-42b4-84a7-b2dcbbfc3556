package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppFeedbackDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppFeedbackQuery;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppFeedbackRemoteService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/11
 **/

@Component
public class MiniAppFeedbackRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppFeedbackRemoteService feedbackRemoteService;

    public PageInfo<MiniAppFeedbackDTO> getMiniAppFeedbackDTOS(MiniAppFeedbackQuery miniAppFeedbackQuery) {
        return call("getMiniAppFeedbackDTOS",
                () -> feedbackRemoteService.getMiniAppFeedbackDTOS(toRequestBody(miniAppFeedbackQuery)));
    }
}
