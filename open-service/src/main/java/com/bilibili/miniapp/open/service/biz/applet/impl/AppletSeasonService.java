package com.bilibili.miniapp.open.service.biz.applet.impl;


import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppAppletPriorityRepository;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppAppletSeasonRepository;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.applet.IAppletSeasonService;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.SeasonAuthorizationExtService;
import com.bilibili.miniapp.open.service.biz.ogv.impl.AuthorAuthorizationService;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrl;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrlParam;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.mapper.MiniAppBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huan
 * @Date: 2025-06-26日 17:49
 * @Description:
 */

@Service
@Slf4j
public class AppletSeasonService implements IAppletSeasonService {

    @Resource
    private IMiniAppAppletSeasonRepository miniAppAppletSeasonRepository;

    @Resource
    private IMiniAppAppletPriorityRepository miniAppAppletPriorityRepository;

    @Resource(name = "redissonCacheRepository")
    private ICacheRepository cacheRepository;

    @Override
    public List<MiniAppBaseInfoBo> batchFetchAppletFromCandidates(List<AppletShortBo> appletShortBos) {

        return List.of();
    }

    @Override
    public MiniAppBaseInfoBo fetchAppletFromCandidates(AppletShortBo appletShortBo) {
        return batchFetchAppletFromCandidates(List.of(appletShortBo)).get(0);
    }

    @Override
    public Map<Long, String> getDefaultAppletBySeasonIds(List<Long> seasonIds) {
        return miniAppAppletSeasonRepository.getSeasonBindingApplets(seasonIds);
    }

    @Override
    public List<Long> getSeasonIdsByAppId(String appId) {
        return miniAppAppletSeasonRepository.getSeasonIdsByAppId(appId);
    }

    @Override
    public void bindSeasonToApplet(Long seasonId, String appId) {
        // 绑定关系
        miniAppAppletSeasonRepository.bindSeasonToApplet(seasonId, appId);
        // 2. 写缓存
        String redisKey = getSeasonBindRedisKey(seasonId);
        cacheRepository.setObject(redisKey, appId);
    }

    @NotNull
    private static String getSeasonBindRedisKey(Long seasonId) {
        return String.format(RedisKeyPattern.OPEN_PLATFORM_APPLET_SEASON_BIND.getPattern(), seasonId);
    }

    @Override
    public List<String> getAppletPriority() {
        return miniAppAppletPriorityRepository.getPriorityAppIds();
    }

    @Override
    public void saveAppletPriority(List<String> appIdList) {
        miniAppAppletPriorityRepository.batchSavePriority(appIdList);
        String priorityKey = getAppletPriorityRedisKey();
        cacheRepository.clearAndAddAllList(priorityKey, appIdList);
    }

    private static String getAppletPriorityRedisKey() {
        return RedisKeyPattern.OPEN_PLATFORM_APPLET_PRIORITY.getPattern();
    }

    @Override
    public Map<Long, String> getDefaultAppletBySeasonIdsFromCache(List<Long> seasonIds) {
        if (CollectionUtils.isEmpty(seasonIds)) {
            return Map.of();
        }
        Map<Long, String> cachedApplets = new HashMap<>();
        // 1. 从缓存中获取
        List<String> redisKeys = seasonIds.stream()
                .map(AppletSeasonService::getSeasonBindRedisKey)
                .collect(Collectors.toList());
        Map<String, String> appIdMap = cacheRepository.multiGetObject(redisKeys, String.class);
        // 2. 缓存没有的从数据库中获取
        List<Long> missingSeasonIds = new ArrayList<>();
        for (Long seasonId : seasonIds) {
            String appId = appIdMap.get(getSeasonBindRedisKey(seasonId));
            if (appId == null) {
                missingSeasonIds.add(seasonId);
            } else {
                cachedApplets.put(seasonId, appId);
            }
        }
        if (CollectionUtils.isNotEmpty(missingSeasonIds)) {
            Map<Long, String> dbResult = miniAppAppletSeasonRepository.getSeasonBindingApplets(missingSeasonIds);
            // 3. 将数据库结果写入缓存
            for (Map.Entry<Long, String> entry : dbResult.entrySet()) {
                Long seasonId = entry.getKey();
                String appId = entry.getValue();
                cachedApplets.put(seasonId, appId);
                String redisKey = getSeasonBindRedisKey(seasonId);
                cacheRepository.setObject(redisKey, appId);
            }
        }
        return cachedApplets;
    }

    @Override
    public List<String> getAppletPriorityFromCache() {
        String redisKey = getAppletPriorityRedisKey();
        return cacheRepository.getList(redisKey, String.class);
    }
}
