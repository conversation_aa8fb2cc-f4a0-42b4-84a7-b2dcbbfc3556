package com.bilibili.miniapp.open.service.biz.applet;

import com.bilibili.miniapp.open.service.bo.applet.AppletCustomizedContext;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrl;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrlParam;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/

public interface IAppletUrlService {

    /**
     * 小程序主页
     * @param appId 小程序id
     * @param appletVersion 小程序框架版本
     * @param params query参数
     * @return
     */
    String getMainPageUrl(String appId, Integer appletVersion, Map<String, Object> params);

    String getSeasonPageUrl(String appId, Integer appletVersion, String path, Map<String, Object> params);

    /**
     * 批量查询 单个season下面所有的
     * @param appletSeasonUrlParam
     * @return seasonId -> Array<AppletSeasonUrl>
     */
    List<AppletSeasonUrl> getBatchAppletSeasonUrls(List<AppletSeasonUrlParam> appletSeasonUrlParam);

    AppletSeasonUrl getSeasonUrl(MiniAppBaseInfoBo appBaseInfoBo, AppletSeasonUrlParam.SeasonEpIds seasonEpId, Integer sourceFrom);

    String buildJumpUrl(AppletCustomizedContext context, String appId, Integer appletVersion, Long seasonId, Long episodeId, Integer sourceFrom);

    AppletCustomizedContext getCustomizedContext(Map<String, String> pathMapConfig, String appId);

    Map<String, AppletCustomizedContext> getCustomizedContextMap(List<String> appIds);

    List<AppletSeasonUrl> getSingleAppletSeasonUrls(MiniAppBaseInfoBo appBaseInfoBo, List<AppletSeasonUrlParam.SeasonEpIds> seasonEpIds, Integer sourceFrom);
}
