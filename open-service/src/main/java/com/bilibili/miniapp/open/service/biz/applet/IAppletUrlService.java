package com.bilibili.miniapp.open.service.biz.applet;

import com.bilibili.miniapp.open.service.bo.applet.AppletCustomizedContext;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrl;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrlParam;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/

public interface IAppletUrlService {

    /**
     * 小程序主页
     * @param appId 小程序id
     * @param appletVersion 小程序框架版本
     * @param params query参数
     * @return
     */
    String getMainPageUrl(String appId, Integer appletVersion, Map<String, Object> params);

    String getSeasonPageUrl(String appId, Integer appletVersion, String path, Map<String, Object> params);

    /**
     * 多个小程序-单个剧集
     * @param appletSeasonUrlParam
     * @return
     */
    List<AppletSeasonUrl> getBatchAppletSeasonUrls(AppletSeasonUrlParam appletSeasonUrlParam);

    /**
     * 单个小程序-多个剧集
     * @param appId
     * @param seasonEpIds
     * @param sourceFrom
     * @return
     */
    List<AppletSeasonUrl> getSeasonUrls(MiniAppBaseInfoBo appBaseInfoBo, List<AppletSeasonUrlParam.SeasonEpId> seasonEpIds, Integer sourceFrom);

    AppletSeasonUrl getSeasonUrl(MiniAppBaseInfoBo appBaseInfoBo, AppletSeasonUrlParam.SeasonEpId seasonEpId, Integer sourceFrom);

    String buildJumpUrl(AppletCustomizedContext context, String appId, Integer appletVersion, Long seasonId, Long episodeId, Integer sourceFrom);

    AppletCustomizedContext getCustomizedContext(Map<String, String> pathMapConfig, String appId);

    Map<String, AppletCustomizedContext> getCustomizedContextMap(List<String> appIds);
}
