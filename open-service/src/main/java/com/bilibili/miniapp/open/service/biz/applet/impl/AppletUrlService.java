package com.bilibili.miniapp.open.service.biz.applet.impl;

import com.alibaba.fastjson2.JSON;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.bo.applet.AppletCustomizedContext;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrl;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonUrlParam;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;
import com.bilibili.miniapp.open.service.common.MiniAppLinkBuilder;
import com.bilibili.miniapp.open.service.config.AppPathConfigCenter;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/

@Service
@Slf4j
public class AppletUrlService implements IAppletUrlService {

    @Resource
    private ConfigCenter configCenter;

    @Resource
    private AppPathConfigCenter pathConfigCenter;

    @Resource
    private IMiniAppCustomLinkService miniAppCustomLinkService;

    public static final int APPLET_VERSION_0 = 0;
    public static final int APPLET_VERSION_1 = 1;

    @Override
    public String getMainPageUrl(String appId, Integer appletVersion, Map<String, Object> params) {
        return new MiniAppLinkBuilder()
                .baseUrl(getBaseUrl(appletVersion))
                .appId(String.valueOf(appId))
                .withParams(params)
                .build();
    }

    @Override
    public String getSeasonPageUrl(String appId, Integer appletVersion, String path, Map<String, Object> params) {
        return new MiniAppLinkBuilder()
                .baseUrl(getBaseUrl(appletVersion))
                .appId(appId)
                .path(path)
                .withParams(params)
                .build();
    }

    @Override
    public List<AppletSeasonUrl> getBatchAppletSeasonUrls(AppletSeasonUrlParam appletSeasonUrlParam) {
        return null;
    }

    @Override
    public List<AppletSeasonUrl> getSeasonUrls(MiniAppBaseInfoBo appBaseInfoBo, List<AppletSeasonUrlParam.SeasonEpId> seasonEpIds, Integer sourceFrom) {
        AssertUtil.notNull(appBaseInfoBo, ErrorCodeType.BAD_PARAMETER.getCode(), "小程序信息不能为空");
        if (CollectionUtils.isEmpty(seasonEpIds)) {
            return Collections.emptyList();
        }
        AppletCustomizedContext context = getCustomizedContext(pathConfigCenter.getP(), appBaseInfoBo.getAppId());
        if (context == null) {
            log.warn("[AppletUrlService] Customized context is null for appId: {}", appBaseInfoBo.getAppId());
            return Collections.emptyList();
        }
        return seasonEpIds.stream().filter(seasonEpId -> NumberUtil.isPositive(seasonEpId.getSeasonId())).map(seasonEpId -> {
            Long seasonId = seasonEpId.getSeasonId();
            Long episodeId = seasonEpId.getEpisodeId();
            AppletSeasonUrl seasonUrl = new AppletSeasonUrl();
            seasonUrl.setAppId(appBaseInfoBo.getAppId());
            seasonUrl.setSeasonId(seasonId);
            seasonUrl.setSeasonUrl(buildJumpUrl(context, appBaseInfoBo.getAppId(), appBaseInfoBo.getAppletVersion(), seasonId, null, sourceFrom));
            if (NumberUtil.isPositive(episodeId)) {
                seasonUrl.setEpisodeId(episodeId);
                seasonUrl.setEpisodeUrl( buildJumpUrl(context, appBaseInfoBo.getAppId(), appBaseInfoBo.getAppletVersion(), seasonId, episodeId, sourceFrom));
            }
            return seasonUrl;
        }).collect(Collectors.toList());
    }

    @Override
    public AppletSeasonUrl getSeasonUrl(MiniAppBaseInfoBo appBaseInfoBo, AppletSeasonUrlParam.SeasonEpId seasonEpId, Integer sourceFrom) {
        List<AppletSeasonUrl> seasonUrls = getSeasonUrls(appBaseInfoBo, Collections.singletonList(seasonEpId), sourceFrom);
        if (CollectionUtils.isEmpty(seasonUrls)) {
            return null;
        }
        return seasonUrls.get(0);
    }

    public String buildJumpUrl(AppletCustomizedContext context, String appId, Integer appletVersion, Long seasonId, Long episodeId, Integer sourceFrom) {
        Map<String, Object> params = new HashMap<>();
        addIfPositive(params, "sourcefrom", sourceFrom);
        addIfPositive(params, "bl_episode_id", episodeId);
        addIfPositive(params, "bl_album_id", seasonId);

        if (Objects.isNull(context)) {
            return null;
        }

        mergeParams(params, context.getParams());

        return getSeasonPageUrl(
                appId,
                appletVersion,
                context.getPath(),
                params
        );
    }

    public Map<String, AppletCustomizedContext> getCustomizedContextMap(List<String> appIds) {
        Map<String, MiniAppCustomLinkBo> customLinkFromCacheByAppIds = miniAppCustomLinkService.getCustomLinkFromCacheByAppIds(appIds);
        Map<String, AppletCustomizedContext> result = new HashMap<>();
        Map<String, String> pathMapConfig = pathConfigCenter.getP();
        appIds.forEach(appId -> result.put(appId, getCustomizedContext(pathMapConfig, appId, customLinkFromCacheByAppIds.get(appId))));
        return result;
    }

    public AppletCustomizedContext getCustomizedContext(Map<String, String> pathMapConfig, String appId) {
        MiniAppCustomLinkBo customLink = miniAppCustomLinkService.getCustomLinkFromCache(appId);
        return getCustomizedContext(pathMapConfig, appId, customLink);
    }

    private AppletCustomizedContext getCustomizedContext(Map<String, String> pathMapConfig, String appId, MiniAppCustomLinkBo customLink) {
        Map<String, Object> params = new HashMap<>();
        String path;
        if (customLink == null) {
            String pathConfigStr = pathMapConfig.get(appId);
            if (StringUtils.isBlank(pathConfigStr)) {
                log.warn("[AppletService] Path config is missing for appId: {}", appId);
                return null;
            }

            AppPathConfigCenter.AppPathConfig pathConfig = parsePathConfig(pathConfigStr);
            if (pathConfig == null || StringUtils.isBlank(pathConfig.getPath())) {
                return null;
            }

            mergeParams(params, pathConfig.getParams());
            path = pathConfig.getPath();
        } else {
            String pathPrefix = "/pages";
            if (StringUtils.isBlank(customLink.getCustomPath())) {
                log.warn("[AppletService] Custom path is missing for appId: {}", appId);
                return null;
            }
            if (customLink.getCustomPath().startsWith("/pages")) {
                pathPrefix = "";
            }
            path = pathPrefix + customLink.getCustomPath();
            addCustomParamsIfNecessary(params, customLink.getCustomParams());
        }
        return AppletCustomizedContext.builder()
                .path(path)
                .params(params)
                .build();
    }

    private void addCustomParamsIfNecessary(Map<String, Object> params, String customParams) {
        if (StringUtils.isNotBlank(customParams)) {
            Arrays.stream(customParams.split("&"))
                    .forEach(customParam -> {
                        String[] split = customParam.split("=");
                        if (split.length == 2) {
                            String key = split[0];
                            String value = split[1];
                            addIfNotNull(params, key, value);
                        }
                    });
        }
    }

    private void addIfNotNull(Map<String, Object> params, String key, Object value) {
        if (value != null) {
            params.put(key, value);
        }
    }

    private void addIfPositive(Map<String, Object> params, String key, Number value) {
        if (NumberUtil.isPositive(value)) {
            params.put(key, value);
        }
    }

    private AppPathConfigCenter.AppPathConfig parsePathConfig(String pathConfigStr) {
        try {
            return JSON.parseObject(pathConfigStr, AppPathConfigCenter.AppPathConfig.class);
        } catch (Exception e) {
            log.error("[AppletService] Failed to parse path config", e);
            return null;
        }
    }

    private void mergeParams(Map<String, Object> target, Map<String, Object> source) {
        if (MapUtils.isNotEmpty(source)) {
            for (Map.Entry<String, Object> entry : source.entrySet()) {
                target.putIfAbsent(entry.getKey(), entry.getValue());
            }
        }
    }

    private String getBaseUrl(Integer appletVersion) {
        // 默认新版本框架
        if (appletVersion == null) {
            appletVersion = APPLET_VERSION_1;
        }
        String jumpUrlBase = configCenter.getUserAccess().getJumpUrlBase_1();
        if (Objects.equals(appletVersion, APPLET_VERSION_0)) {
            jumpUrlBase = configCenter.getUserAccess().getJumpUrlBase_0();
        }
        return jumpUrlBase;
    }
}
