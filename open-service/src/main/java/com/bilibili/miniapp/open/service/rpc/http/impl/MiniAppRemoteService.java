package com.bilibili.miniapp.open.service.rpc.http.impl;

import cn.hutool.core.util.StrUtil;
import com.bilibili.mall.miniapp.dto.miniapp.*;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.mall.miniapp.query.miniapp.*;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.MiniAppConfig;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppRemoteService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Component
@Slf4j
public class MiniAppRemoteService extends AbstractOpenService {

    @Autowired
    private IMiniAppRemoteService miniAppRemoteService;

    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cache;

    @Autowired
    private ConfigCenter configCenter;

    public ChannelMiniAppInfoDTO updateMiniApp(MiniAppInfoUpdateQuery miniAppInfoUpdateQuery) {
        return call("更新小程序",
                miniAppRemoteService::updateInfo,
                miniAppInfoUpdateQuery);
    }

    public boolean updateMiniAppAllowPay(MiniAppPayUpdateQuery miniAppPayUpdateQuery) {
        return call("更新小程序是否允许支付",
                miniAppRemoteService::updateInternalMiniAppAllowPay,
                miniAppPayUpdateQuery);
    }

    public MiniAppDTO createMiniApp(MiniAppAddQuery miniAppAddQuery) {
        return call("创建小程序",
                miniAppRemoteService::createMiniApp,
                miniAppAddQuery);
    }

    public PageInfo<MiniAppDTO> listMiniApps(MiniAppQuery miniAppQuery) {
        return call("查询小程序列表",
                miniAppRemoteService::listMiniAppDTOS,
                miniAppQuery);
    }

    public List<MiniAppDTO> listMiniAppsByAppIds(List<String> appIds){
        if (CollectionUtils.isEmpty(appIds)) {
            return new ArrayList<>();
        }
        return call("查询指定appIds创建的小程序",
                () -> miniAppRemoteService.listMiniAppsByAppIds(appIds));
    }

    public MiniAppDTO queryAppInfo(String appId){
        return call(
                StrUtil.format("获取指定小程序[{}]详情", appId),
                ()->miniAppRemoteService.getMiniAppDTO(appId,null ));
    }

    public ChannelMiniAppInfoDTO getMiniAppInfoDTO(MiniAppInfoQuery query) {
        return call("获取小程序信息",
                () -> miniAppRemoteService.getMiniAppInfoDTO(toRequestBody(query)));
    }

    public List<MiniAppCheckDTO> nameCheck(String name) {
        return call("检查小程序名称",
                () -> miniAppRemoteService.nameCheck(name));
    }

    public List<MiniAppCheckDTO> nameRepeat(String name) {
        return call("小程序名称重复",
                () -> miniAppRemoteService.nameRepeat(name));
    }

    public Boolean updateName(String appId, String name) {
        return call("修改小程序名称",
                () -> miniAppRemoteService.updateName(appId, name));
    }

    public PageResult<MiniAppBaseInfoDto> queryMiniAppBaseInfosPage(MiniAppBaseInfoQueryParamDto query) {
        return call("查询小程序基本信息分页",
                () -> miniAppRemoteService.getMiniAppBaseInfosPage(toRequestBody(query)));
    }


    public ChannelMiniAppInfoDTO queryAppInfoWithinCache(String appId) {
        String cacheKey = buildMiniAppCacheKey(appId);

        ChannelMiniAppInfoDTO cachedMiniAppDTO = cache.getObject(cacheKey, ChannelMiniAppInfoDTO.class);
        if (cachedMiniAppDTO != null) {
            return cachedMiniAppDTO;
        }

        ChannelMiniAppInfoDTO miniAppDTO = call(
                StrUtil.format("获取指定小程序[{}]详情", appId),
                () -> {
                    MiniAppInfoQuery miniAppInfoQuery = new MiniAppInfoQuery();
                    miniAppInfoQuery.setAppId(appId);
                    return miniAppRemoteService.getMiniAppInfoDTO(toRequestBody(miniAppInfoQuery));
                }
        );

        cache.setObject(cacheKey, miniAppDTO, 5, TimeUnit.MINUTES);
        return miniAppDTO;
    }

    private String buildMiniAppCacheKey(String appid) {
        return StrUtil.format("miniapp:appid:{}", appid);
    }

    /**
     * 批量获取小程序信息
     * @param appIds
     * @return
     */
    public Map<String, MiniAppBaseInfoDto> queryAppInfosWithinCache(List<String> appIds) {
        Map<String, MiniAppBaseInfoDto> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(appIds)) {
            return resultMap;
        }

        // 优先从缓存获取
        List<String> keys = new ArrayList<>();
        appIds.forEach(key -> keys.add(buildMiniAppBaseCacheKey(key)));
        Map<String, MiniAppBaseInfoDto> map = cache.multiGetObject(keys, MiniAppBaseInfoDto.class);
        // 找出未命中缓存的id
        List<String> unCachedIds = new ArrayList<>();
        for (String appId : appIds) {
            String key = buildMiniAppBaseCacheKey(appId);
            MiniAppBaseInfoDto miniAppInfoDTO = map.get(key);
            if (miniAppInfoDTO == null) {
                unCachedIds.add(appId);
            } else {
                resultMap.put(appId, miniAppInfoDTO);
            }
        }
        if (CollectionUtils.isEmpty(unCachedIds)) {
            return resultMap;
        }

        // 从数据库获取
        List<MiniAppBaseInfoDto> baseInfoDtos = call(
                StrUtil.format("批量获取小程序信息[{}]", unCachedIds),
                () -> {
                    MiniAppQueryInfo miniAppQueryInfo = new MiniAppQueryInfo();
                    miniAppQueryInfo.setAppIds(unCachedIds);
                    return miniAppRemoteService.getMiniAppBaseInfos(toRequestBody(miniAppQueryInfo));
                });

        if (CollectionUtils.isNotEmpty(baseInfoDtos)) {
            MiniAppConfig miniAppConfig = configCenter.getMiniAppConfig();
            Map<String, MiniAppBaseInfoDto> appIdKeyMap = baseInfoDtos.stream().collect(Collectors.toMap(key -> buildMiniAppBaseCacheKey(key.getAppId()), Function.identity(), (v1, v2) -> v1));
            Map<String, MiniAppBaseInfoDto> baseInfoDtoMap = baseInfoDtos.stream().collect(Collectors.toMap(MiniAppBaseInfoDto::getAppId, Function.identity(), (v1, v2) -> v1));
            cache.multiSetObject(appIdKeyMap, miniAppConfig.getExpireTime(), ChronoUnit.MINUTES, miniAppConfig.getExpireJitterPercent());
            resultMap.putAll(baseInfoDtoMap);
        }
        return resultMap;
    }

    private String buildMiniAppBaseCacheKey(String appid) {
        return StrUtil.format("miniapp:appid:base:{}", appid);
    }

    public void refreshMiniAppCache(List<String> appIds) {
        // 先查询
        try {
            List<MiniAppBaseInfoDto> baseInfoDtos = call(
                    StrUtil.format("批量获取小程序信息[{}]", appIds),
                    () -> {
                        MiniAppQueryInfo miniAppQueryInfo = new MiniAppQueryInfo();
                        miniAppQueryInfo.setAppIds(appIds);
                        return miniAppRemoteService.getMiniAppBaseInfos(toRequestBody(miniAppQueryInfo));
                    });
            if (CollectionUtils.isEmpty(baseInfoDtos)) {
                log.info("[MiniAppRemoteService] refreshMiniAppCache, appIds={}, no data", appIds);
                return;
            }
            MiniAppConfig miniAppConfig = configCenter.getMiniAppConfig();
            Map<String, MiniAppBaseInfoDto> appIdKeyMap = baseInfoDtos.stream().collect(Collectors.toMap(key -> buildMiniAppBaseCacheKey(key.getAppId()), Function.identity(), (v1, v2) -> v1));
            cache.multiSetObject(appIdKeyMap, miniAppConfig.getExpireTime(), ChronoUnit.MINUTES, miniAppConfig.getExpireJitterPercent());
            log.info("[MiniAppRemoteService] refreshMiniAppCache, appIds={}, cache size={}", appIds, appIdKeyMap.size());
        } catch (Exception e) {
            log.error("[MiniAppRemoteService] refreshMiniAppCache, appIds={}, error", appIds, e);
        }
    }

    public Map<String, MiniAppBaseInfoDto> queryAppInfos(List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyMap();
        }

        MiniAppQueryInfo miniAppQueryInfo = new MiniAppQueryInfo();
        miniAppQueryInfo.setAppIds(appIds);
        List<MiniAppBaseInfoDto> baseInfoDtos = call(
                StrUtil.format("批量获取小程序信息[{}]", appIds),
                () -> miniAppRemoteService.getMiniAppBaseInfos(toRequestBody(miniAppQueryInfo)));

        if (CollectionUtils.isEmpty(baseInfoDtos)) {
            return Collections.emptyMap();
        }

        return baseInfoDtos.stream().collect(Collectors.toMap(MiniAppBaseInfoDto::getAppId, Function.identity(), (v1, v2) -> v1));
    }
}
