package com.bilibili.miniapp.open.service.biz.applet;


import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.applet.*;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;

import java.util.List;

/**
 * @author: huan
 * @Date: 2025-06-30 18:00
 * @Description: 小程序网关平台接口（B端）
 */
public interface IAppletPlatformService {

    PageResult<AppletSeasonInfoBo> getAppletSeasonInfoList(AppletSeasonQueryParam queryParam);

    /**
     * 获取小程序短剧剧集列表（不包含集）
     * @param queryParam
     * @return
     */
    PageResult<AppletSeasonBo> getAppletSeasonList(AuthorSeasonQueryParam queryParam) throws Exception;

    AppletSeasonInfoBo getAppletSeasonInfo(String appId, Long seasonId, Long episodeId, Integer sourceFrom) throws Exception;

    void bindAppletSeason(String appId, Long seasonId);

    List<AppletPriorityBo> getAppletPriority();

    void saveAppletPriority(List<String> appIdList);

    List<UserInfoBo> getAllAuthor();
}
