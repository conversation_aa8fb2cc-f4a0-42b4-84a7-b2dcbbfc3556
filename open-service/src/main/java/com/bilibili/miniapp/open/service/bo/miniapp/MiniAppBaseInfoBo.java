package com.bilibili.miniapp.open.service.bo.miniapp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniAppBaseInfoBo {

    private String appId;

    private String name;

    private String logo;

    private String introduction;

    private Integer appletVersion;

    private Integer developType;

    private Long mid;

    private String companyId;

    private String companyName;

    private String linkUrl;

    private Integer categoryId;

    private String categoryName;
}
