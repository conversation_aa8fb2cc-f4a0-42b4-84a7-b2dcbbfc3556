package com.bilibili.miniapp.open.service.rpc.http.dto;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class ContractApplyTemplateDto {

    /**
     * 示例如下，是个jsonString
     * [
     *   {
     *     "name": "乙方-基本信息-联系地址",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "乙方-基本信息-联系人",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "乙方-基本信息-手机",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "乙方-基本信息-电子邮件",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "起始年",
     *     "read_only": false,
     *     "value": "2025"
     *   },
     *   {
     *     "name": "起始月",
     *     "read_only": false,
     *     "value": "6"
     *   },
     *   {
     *     "name": "起始日",
     *     "read_only": false,
     *     "value": "9"
     *   },
     *   {
     *     "name": "结束年",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "结束月",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "结束日",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "乙方-基本信息-企业名称",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "签署年",
     *     "read_only": false,
     *     "value": "2025"
     *   },
     *   {
     *     "name": "签署月",
     *     "read_only": false,
     *     "value": "6"
     *   },
     *   {
     *     "name": "签署日",
     *     "read_only": false,
     *     "value": "9"
     *   },
     *   {
     *     "name": "乙方-基本信息-小程序APPID",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "乙方-基本信息-小程序名称",
     *     "read_only": false,
     *     "value": ""
     *   },
     *   {
     *     "name": "合同编号",
     *     "read_only": true,
     *     "value": "CTR570324251945510490"
     *   }
     * ]
     */
    private String form_info;
}
