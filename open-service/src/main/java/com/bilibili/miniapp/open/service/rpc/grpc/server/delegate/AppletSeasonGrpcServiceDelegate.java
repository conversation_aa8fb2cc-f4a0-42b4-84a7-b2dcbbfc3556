package com.bilibili.miniapp.open.service.rpc.grpc.server.delegate;

import com.bapis.ad.applet.Pageable;
import com.bapis.ad.applet.platform.season.*;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.biz.applet.IAppletService;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonQueryParam;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.server.AbstractGrpcService;
import io.grpc.stub.StreamObserver;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Service
public class AppletSeasonGrpcServiceDelegate extends AbstractGrpcService  {

    @Resource
    private IAppletService appletService;

    public void queryAppletSeasonInfo(QueryAppletSeasonInfoRequest request, StreamObserver<GetAppletSeasonInfoResponse> responseObserver) {
        process(
                request, // 请求
                responseObserver, // 响应
                "queryAppletSeasonInfo", // 方法名
                (req, context) -> doQueryAppletSeasonInfo(req.getQueryAppletSeasonInfoParam()), // 实际业务逻辑处理
                e -> GetAppletSeasonInfoResponse.newBuilder()
                        .setCtx(defaultErrorResponseContext(e))
                        .build()
        );
    }

    public void getAppletSeasonInfoById(GetAppletSeasonInfoByIdRequest request, StreamObserver<GetAppletSeasonInfoByIdResponse> responseObserver) {
        process(
                request, // 请求
                responseObserver, // 响应
                "getAppletSeasonInfoById", // 方法名
                (req, context) -> doGetAppletSeasonInfoById(req.getGetAppletSeasonParam()), // 实际业务逻辑处理
                e -> GetAppletSeasonInfoByIdResponse.newBuilder()
                        .setCtx(defaultErrorResponseContext(e))
                        .build()
        );
    }

    private GetAppletSeasonInfoResponse doQueryAppletSeasonInfo(QueryAppletSeasonInfoParam queryAppletSeasonInfoParam) {
        // 调用业务逻辑层查询小程序剧集信息
        Pageable pageable = queryAppletSeasonInfoParam.getPageable();
        Page page = convertToPage(pageable);
        PageResult<AppletSeasonInfoBo> seasonInfoList = appletService.getAppletSeasonInfoList(AppletSeasonQueryParam.builder()
                .appId(queryAppletSeasonInfoParam.getAppId())
                .seasonName(queryAppletSeasonInfoParam.getSeasonName())
                .sourceFrom(queryAppletSeasonInfoParam.getSourceFrom().getValue())
                .page(page)
                .appendEpisode(queryAppletSeasonInfoParam.getAppendEpisode())
                .build());
        if (CollectionUtils.isEmpty(seasonInfoList.getRecords())) {
            return GetAppletSeasonInfoResponse.newBuilder()
                    .setPageableAppletSeasonInfo(PageableAppletSeasonInfo.newBuilder()
                            .setPageable(Pageable.newBuilder().setTotalCount(0))
                            .build())
                    .build();
        }
        return GetAppletSeasonInfoResponse.newBuilder()
                .setPageableAppletSeasonInfo(PageableAppletSeasonInfo.newBuilder()
                        .addAllSeasonInfo(seasonInfoList.getRecords().stream().map(this::convertToAppletSeasonInfo).collect(Collectors.toList()))
                        .setPageable(Pageable.newBuilder().setTotalCount(seasonInfoList.getTotal()))
                        .build())
                .build();
    }

    private AppletSeasonInfo convertToAppletSeasonInfo(AppletSeasonInfoBo appletSeasonInfoBo) {
        MiniAppBaseInfoBo miniAppBaseInfo = appletSeasonInfoBo.getMiniAppBaseInfo();
        AppletSeasonInfo.Builder builder = AppletSeasonInfo.newBuilder()
                .setAppId(miniAppBaseInfo.getAppId())
                .setAppName(miniAppBaseInfo.getName())
                .setAppIcon(miniAppBaseInfo.getLogo())
                .setAppDesc(miniAppBaseInfo.getIntroduction())
                .setTitle(appletSeasonInfoBo.getTitle())
                .setCover(appletSeasonInfoBo.getCover())
                .setSeasonId(appletSeasonInfoBo.getSeasonId())
                .setLinkUrl(appletSeasonInfoBo.getLinkUrl());
        if (CollectionUtils.isNotEmpty(appletSeasonInfoBo.getEpisodes())) {
            builder.addAllEpisodes(appletSeasonInfoBo.getEpisodes().stream()
                    .map(episode -> AppletEpisodeInfo.newBuilder()
                            .setEpisodeId(episode.getEpisodeId())
                            .setTitle(episode.getTitle())
                            .setCover(episode.getCover())
                            .setLinkUrl(episode.getLinkUrl())
                            .setLongTitle(episode.getLongTitle())
                            .build())
                    .collect(Collectors.toList()));
        }
        return builder.build();
    }

    private GetAppletSeasonInfoByIdResponse doGetAppletSeasonInfoById(GetAppletSeasonParam getAppletSeasonParam) throws Exception {
        AppletSeasonInfoBo appletSeasonInfo = appletService.getAppletSeasonInfo(getAppletSeasonParam.getAppId(), getAppletSeasonParam.getSeasonId(), getAppletSeasonParam.getEpisodeId().getValue(), getAppletSeasonParam.getSourceFrom().getValue());
        if (appletSeasonInfo != null) {
            return GetAppletSeasonInfoByIdResponse.newBuilder()
                    .setSeasonInfo(convertToAppletSeasonInfo(appletSeasonInfo))
                    .build();
        }
        return GetAppletSeasonInfoByIdResponse.newBuilder().build();
    }
}
