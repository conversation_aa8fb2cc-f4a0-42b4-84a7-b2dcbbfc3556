package com.bilibili.miniapp.open.service.biz.applet;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.applet.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/
public interface IAppletService {

    /**
     * 获取小程序短剧信息列表
     * @return
     */
    List<AppletShortBo> getAppletShortInfoList(AppletShortContext context);

    AppletInfoBo getAppletInfo(AppletShortQueryParam queryParam) throws Exception;

    void refreshAppletInfoCache() throws Exception;

    PageResult<AppletSeasonInfoBo> getAppletSeasonInfoList(AppletSeasonQueryParam queryParam);

    AppletSeasonInfoBo getAppletSeasonInfo(String appId, Long seasonId, Long episodeId, Integer sourceFrom) throws Exception;
}
