package com.bilibili.miniapp.open.service.biz.ogv.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.AppDevelopType;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonVideoViewSummaryDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonVideoViewSummaryPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonVideoViewSummaryPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.biz.applet.IAppletSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonRecommendService;
import com.bilibili.miniapp.open.service.biz.ogv.SeasonAuthorizationExtService;
import com.bilibili.miniapp.open.service.bo.ogv.RecommendAppBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Service
public class SeasonRecommendService implements ISeasonRecommendService {

    @Autowired
    private MiniAppOpenSeasonVideoViewSummaryDao seasonVideoViewDao;
    @Autowired
    private SeasonAuthorizationExtService authorizationExtService;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;
    @Autowired
    private OgvSeasonService ogvSeasonService;
    @Autowired
    private IAppletSeasonService appletSeasonService;

    @Override
    public PageResult<RecommendAppBo> recommendSeasons(Long fromSeasonId, Page page) {
        PageResult<Long> hotSeasonPageResult = queryHotSeasonIds(fromSeasonId, page);
        if (hotSeasonPageResult.getTotal() == 0) {
            return PageResult.emptyPageResult();
        }

        List<RecommendAppBo> records = findRecommendMiniAppBySeasonIds(hotSeasonPageResult.getRecords());
        return PageResult.<RecommendAppBo>builder()
                .total(hotSeasonPageResult.getTotal())
                .records(records)
                .build();
    }

    public List<RecommendAppBo> findRecommendMiniAppBySeasonIds(List<Long> seasonIds) {
        Map<Long, List<SeasonAuthorizationResultBo>> authorizedMap = authorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
        if (MapUtils.isEmpty(authorizedMap)) {
            return null;
        }
        List<RecommendAppBo> result = new ArrayList<>();
        authorizedMap.forEach((seasonId, seasonAuthInfos) -> {
            if (CollectionUtils.isEmpty(seasonAuthInfos)) {
                return;
            }

            RecommendAppBo recommendMiniApp = findRecommendMiniApp(seasonId, seasonAuthInfos);
            if (recommendMiniApp != null) {
                result.add(recommendMiniApp);
            }
        });

        return result;
    }

    private RecommendAppBo findRecommendMiniApp(long seasonId, List<SeasonAuthorizationResultBo> seasonAuthInfos) {
        List<String> appIds = seasonAuthInfos.stream()
                .filter(auth -> Objects.equals(auth.getSeasonId(), seasonId))
                .map(SeasonAuthorizationResultBo::getAppId)
                .collect(Collectors.toList());
        Map<String, MiniAppBaseInfoDto> appMap = miniAppRemoteService.queryAppInfosWithinCache(appIds);

        String recommendAppId = findRecommendAppId(seasonId, appMap, seasonAuthInfos);
        if (recommendAppId == null) {
            return null;
        }
        MiniAppBaseInfoDto recommendApp = appMap.get(recommendAppId);
        SeasonBo seasonBo = getSeasonBo(seasonId);
        if (seasonBo == null) {
            return null;
        }
        return RecommendAppBo.builder()
                .appId(recommendAppId)
                .appName(recommendApp.getName())
                .appLogo(recommendApp.getLogo())
                .season(seasonBo)
                .build();
    }

    private String findRecommendAppId(long seasonId,
                                      Map<String, MiniAppBaseInfoDto> appMap,
                                      List<SeasonAuthorizationResultBo> seasonAuthInfos) {

        List<SeasonAuthorizationResultBo> templateAppAuthInfos = seasonAuthInfos.stream()
                .filter(auth -> {
                    String appId = auth.getAppId();
                    MiniAppBaseInfoDto appInfo = appMap.get(appId);
                    return appInfo != null && appInfo.getDevelopType() == AppDevelopType.TEMPLATE.getCode();
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(templateAppAuthInfos)) {
            return null;
        }

        Map<Long, String> taggedAppMap = appletSeasonService.getDefaultAppletBySeasonIdsFromCache(List.of(seasonId));
        String taggedAppId = taggedAppMap.get(seasonId);

        String recommendAppId;
        boolean taggedAppIsTemplate = templateAppAuthInfos.stream().anyMatch(auth -> Objects.equals(auth.getAppId(), taggedAppId));
        if (taggedAppIsTemplate) {
            recommendAppId = taggedAppId;
        } else {
            SeasonAuthorizationResultBo newestAuthOne = seasonAuthInfos.stream()
                    .max(Comparator.comparing(SeasonAuthorizationResultBo::getAuthTime))
                    .orElse(null);
            if (newestAuthOne == null) {
                return null;
            }
            recommendAppId = newestAuthOne.getAppId();
        }
        return recommendAppId;
    }

    private SeasonBo getSeasonBo(long seasonId) {
        Map<Long, SeasonBo> seasonMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(List.of(seasonId))
                .appendEpisode(true)
                .appendVideoDimension(true)
                .build());
        return seasonMap.get(seasonId);
    }

    public PageResult<Long> queryHotSeasonIds(Long excludeSeasonId, Page page) {

        MiniAppOpenSeasonVideoViewSummaryPoExample example = new MiniAppOpenSeasonVideoViewSummaryPoExample();
        MiniAppOpenSeasonVideoViewSummaryPoExample.Criteria criteria = example.createCriteria();

        criteria.andIsDeletedEqualTo(0);
        if (excludeSeasonId != null) {
            criteria.andSeasonIdNotEqualTo(excludeSeasonId);
        }

        long count = seasonVideoViewDao.countByExample(example);
        if (count == 0) {
            return PageResult.emptyPageResult();
        }

        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        example.setOrderByClause("video_view desc");

        List<MiniAppOpenSeasonVideoViewSummaryPo> pos = seasonVideoViewDao.selectByExample(example);
        return PageResult.<Long>builder()
                .total(Math.toIntExact(count))
                .records(pos.stream()
                        .map(MiniAppOpenSeasonVideoViewSummaryPo::getSeasonId)
                        .collect(Collectors.toList()))
                .build();
    }
}
