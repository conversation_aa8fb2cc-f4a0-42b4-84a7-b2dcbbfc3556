package com.bilibili.miniapp.open.service.biz.order.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppExchangeInfoDTO;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.common.entity.LockOption;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.common.util.TraceUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenOrderDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenOrderExtraDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRefundDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRefundExtraDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.*;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.auth.IMiniAppAuthService;
import com.bilibili.miniapp.open.service.biz.order.IOrderIdGenerationService;
import com.bilibili.miniapp.open.service.biz.order.IOrderService;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import com.bilibili.miniapp.open.service.bo.order.*;
import com.bilibili.miniapp.open.service.eventbus.IOpenEventPublisher;
import com.bilibili.miniapp.open.service.eventbus.OpenEvent;
import com.bilibili.miniapp.open.service.eventbus.OpenEventPublisher;
import com.bilibili.miniapp.open.service.eventbus.event.OpenOrderEvent;
import com.bilibili.miniapp.open.service.mapper.OrderMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/15 21:16
 */
@Slf4j
@Service
public class OrderService implements IOrderService {
    @Autowired
    private MiniAppOpenOrderDao openOrderDao;
    @Autowired
    private MiniAppOpenOrderExtraDao openOrderExtraDao;
    @Autowired
    private MiniAppOpenRefundDao openOrderRefundDao;
    @Autowired
    private MiniAppOpenRefundExtraDao openOrderRefundExtraDao;
    @Autowired
    private IOpenAccessService openAccessService;
    @Autowired
    private IOrderIdGenerationService orderIdGenerationService;
    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;
    @Resource(type = OpenEventPublisher.class)
    private IOpenEventPublisher<OpenEvent> openEventPublisher;
    @Resource
    private IMiniAppAuthService authService;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Override
    public OrderCreateRes createOrder(OrderCreateReq req) throws Exception {
        OrderService orderService = (OrderService) AopContext.currentProxy();
        OrderCreateRes orderCreateRes = orderService.doCreateOrder(req);
        OpenOrderEvent openOrderEvent = OpenOrderEvent.builder()
                .actionType(OpenEvent.ActionType.ADD)
                .order(orderService.getOrder(orderCreateRes.getOrderId()))
                .build();
        openEventPublisher.publishWithoutEx(openOrderEvent);
        return orderCreateRes;
    }

    @Override
    public Order getOrder(Long orderId) {
        return getOrderByOrderId(null, orderId);
    }

    @Override
    public Order getOrderByOrderId(String appId, Long orderId) {
        if (!NumberUtil.isPositive(orderId)) {
            return null;
        }
        MiniAppOpenOrderPoExample example = new MiniAppOpenOrderPoExample();
        MiniAppOpenOrderPoExample.Criteria condition = example.createCriteria()
                .andOrderIdEqualTo(orderId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appId)) {
            condition.andAppIdEqualTo(appId);
        }
        List<MiniAppOpenOrderPo> miniAppOpenOrderPos = openOrderDao.selectByExample(example);
        if (CollectionUtils.isEmpty(miniAppOpenOrderPos)) {
            return null;
        }
        Order order = OrderMapper.MAPPER.toOrder(miniAppOpenOrderPos.get(0));
        order.setExtra(this.getOrderExtra(order.getOrderId()));
        return order;
    }

    @Override
    public Order getOrderByDevOrderId(String appId, String devOrderId) {
        if (!(StringUtils.hasText(appId) && StringUtils.hasText(devOrderId))) {
            return null;
        }
        MiniAppOpenOrderPoExample example = new MiniAppOpenOrderPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDevOrderIdEqualTo(devOrderId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenOrderPo> miniAppOpenOrderPos = openOrderDao.selectByExample(example);
        if (CollectionUtils.isEmpty(miniAppOpenOrderPos)) {
            return null;
        }

        Order order = OrderMapper.MAPPER.toOrder(miniAppOpenOrderPos.get(0));
        order.setExtra(this.getOrderExtra(order.getOrderId()));
        return order;
    }

    @Override
    public OrderExtra getOrderExtra(Long orderId) {
        MiniAppOpenOrderExtraPoExample orderExtraExample = new MiniAppOpenOrderExtraPoExample();
        orderExtraExample.createCriteria()
                .andOrderIdEqualTo(orderId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenOrderExtraPo> miniAppOpenOrderExtraPos = openOrderExtraDao.selectByExample(orderExtraExample);
        if (CollectionUtils.isEmpty(miniAppOpenOrderExtraPos)) {
            return null;
        }
        return OrderMapper.MAPPER.toOrderExtra(miniAppOpenOrderExtraPos.get(0));
    }


    @Override
    public void updateOrder(Order order) throws Exception {
        OrderService orderService = (OrderService) AopContext.currentProxy();
        orderService.doUpdateOrder(order);
        OpenOrderEvent openOrderEvent = OpenOrderEvent.builder()
                .actionType(OpenEvent.ActionType.MODIFY)
                .order(orderService.getOrder(order.getOrderId()))
                .build();
        openEventPublisher.publishWithoutEx(openOrderEvent);
    }

    @Transactional(rollbackFor = Exception.class)
    public OrderCreateRes doCreateOrder(OrderCreateReq req) throws Exception {
        log.info("[OrderService] createOrder request={}", JSON.toJSONString(req));
        ValidationCreateContext validationContext = validateCreate(req);
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_ORDER_CREATION.getPattern(), req.getAppId(), req.getDevOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在创建中，请勿重复请求")
                    .build());
            Order order = getOrderByDevOrderId(req.getAppId(), req.getDevOrderId());
            AssertUtil.isNull(order, ErrorCodeType.EXISTS_DATA.getCode(), "订单已存在，请勿重复创建");
            MiniAppExchangeInfoDTO miniAppExchangeInfo = validationContext.getMiniAppExchangeInfo();
            ChannelMiniAppInfoDTO appInfo = miniAppRemoteService.queryAppInfoWithinCache(req.getAppId());
            MiniAppOpenOrderPo openOrderPo = MiniAppOpenOrderPo.builder()
                    .orderId(orderIdGenerationService.generateOrderId())
                    .devOrderId(req.getDevOrderId())
                    .appId(req.getAppId())
                    .openId(req.getOpenId())
                    .mid(miniAppExchangeInfo.getMid())
                    .accessKey(req.getAccessKey())
                    .productType(validationContext.getProductType().getCode())
                    .productId(req.getProductId())
                    .productName(getProductName(appInfo.getName(), req.getProductName()))
                    .productDesc(req.getProductDesc())
                    .amount(req.getAmount())
                    .notifyUrl(req.getNotifyUrl())
                    .settleRatio(100)//默认都可结算
                    .distRatio(0)//默认不分账
                    .orderStatus(OrderStatus.INIT.getCode())
                    .settleStatus(SettleStatus.NO.getCode())
                    .payStatus(PayStatus.NO.getCode())
                    .notifyStatus(NotifyStatus.NO.getCode())
                    .traceId(TraceUtil.genTraceId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .build();
            openOrderDao.insertSelective(openOrderPo);

            MiniAppOpenOrderExtraPo openOrderExtraPo = MiniAppOpenOrderExtraPo.builder()
                    .orderId(openOrderPo.getOrderId())
                    .devExtraData(req.getExtraData())
                    .build();
            openOrderExtraDao.insertSelective(openOrderExtraPo);
            return OrderCreateRes.builder()
                    .openId(openOrderPo.getOpenId())
                    .appId(openOrderPo.getAppId())
                    .mid(openOrderPo.getMid())
                    .orderId(openOrderPo.getOrderId())
                    .devOrderId(openOrderPo.getDevOrderId())
                    .build();
        } finally {
            if (Objects.nonNull(lock)) {
                //lock.unlock();
                //如果上述逻辑执行耗时超过了租赁时间，那么释放锁会报错，导致事务回滚，不过这个是合理的，因为可能这个锁定的资源已经被破坏了，应该被回滚，
                //但是为了debug测试，暂时先catch unlock的异常
                try {
                    lock.unlock();
                } catch (Exception e) {

                }
            }
        }
    }

    private String getProductName(String appName, String originalDesc) {
        return StrUtil.format("【{}】{}", appName, originalDesc);
    }

    @Transactional(rollbackFor = Exception.class)
    public void doUpdateOrder(Order order) throws Exception {
        AssertUtil.notNull(order, ErrorCodeType.BAD_DATA.getCode(), "订单不能为空");
        AssertUtil.isTrue(NumberUtil.isPositive(order.getOrderId()), ErrorCodeType.BAD_DATA.getCode(), "订单不能为空");
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_ORDER_UPDATE.getPattern(), order.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在修改中，请勿重复操作")
                    .build());

            MiniAppOpenOrderPo miniAppOpenOrderPo = new MiniAppOpenOrderPo();
            BeanUtils.copyProperties(order, miniAppOpenOrderPo);
            MiniAppOpenOrderPoExample orderExample = new MiniAppOpenOrderPoExample();
            orderExample.createCriteria()
                    .andOrderIdEqualTo(order.getOrderId())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            openOrderDao.updateByExampleSelective(miniAppOpenOrderPo, orderExample);

            if (Objects.nonNull(order.getExtra())) {
                OrderExtra orderExtra = order.getExtra();
                orderExtra.setOrderId(order.getOrderId());//强制修正下，防止上游不一致
                MiniAppOpenOrderExtraPo miniAppOpenOrderExtraPo = new MiniAppOpenOrderExtraPo();
                BeanUtils.copyProperties(orderExtra, miniAppOpenOrderExtraPo);

                MiniAppOpenOrderExtraPoExample orderExtraExample = new MiniAppOpenOrderExtraPoExample();
                orderExtraExample.createCriteria()
                        .andOrderIdEqualTo(orderExtra.getOrderId())
                        .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                openOrderExtraDao.updateByExampleSelective(miniAppOpenOrderExtraPo, orderExtraExample);
            }
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    private ValidationCreateContext validateCreate(OrderCreateReq req) throws Exception {
        AssertUtil.notNull(req, ErrorCodeType.BAD_DATA.getCode(), "创单请求不能为空");
        AssertUtil.hasText(req.getDevOrderId(), ErrorCodeType.BAD_DATA.getCode(), "订单id不能为空");
        AssertUtil.hasText(req.getAppId(), ErrorCodeType.BAD_DATA.getCode(), "app_id不能为空");
        AssertUtil.hasText(req.getOpenId(), ErrorCodeType.BAD_DATA.getCode(), "open_id不能为空");
        AssertUtil.hasText(req.getAccessKey(), ErrorCodeType.BAD_DATA.getCode(), "access_key不能为空");
        AssertUtil.hasText(req.getProductId(), ErrorCodeType.BAD_DATA.getCode(), "product_id不能为空");
        AssertUtil.hasText(req.getProductName(), ErrorCodeType.BAD_DATA.getCode(), "product_name不能为空");
        AssertUtil.isTrue(NumberUtil.isPositive(req.getAmount()), ErrorCodeType.BAD_DATA.getCode(), "amount必须大于0");
        //特殊符号检查
        AssertUtil.notContainsAny(req.getProductName(), new char[]{'%', '&'}, ErrorCodeType.BAD_DATA.getCode(), "product_name不能包含'%'、'&'等特殊符号");
        if (StringUtils.hasText(req.getProductDesc())) {
            AssertUtil.notContainsAny(req.getProductDesc(), new char[]{'%', '&'}, ErrorCodeType.BAD_DATA.getCode(), "product_desc不能包含'%'、'&'等特殊符号");
        }
        if (StringUtils.hasText(req.getNotifyUrl())) {
            AssertUtil.notContainsAny(req.getNotifyUrl(), new char[]{'?'}, ErrorCodeType.BAD_DATA.getCode(), "notify_url不能携带任何参数，请检查是否包含'?'");
        }
        OpenAccessBo openAccess = openAccessService.getAccess(req.getAccessKey());
        AssertUtil.notNull(openAccess, ErrorCodeType.BAD_DATA.getCode(), "access_key非法");
        //如果开发者传了枚举之外的商品类型，则默认为未知
        ProductType productType = ProductType.getByCodeWithoutEx(req.getProductType());
        MiniAppExchangeInfoDTO miniAppExchangeInfo = authService.getMiniAppExchangeInfoSafely(req.getAppId(), req.getOpenId());
        AssertUtil.notNull(miniAppExchangeInfo, ErrorCodeType.BAD_DATA.getCode(), "open_id非法");

        return ValidationCreateContext.builder()
                .miniAppExchangeInfo(miniAppExchangeInfo)
                .openAccess(openAccess)
                .productType(productType)
                .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ValidationCreateContext {
        private MiniAppExchangeInfoDTO miniAppExchangeInfo;
        private OpenAccessBo openAccess;
        private ProductType productType;
    }
}
