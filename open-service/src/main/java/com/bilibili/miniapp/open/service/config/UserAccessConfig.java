package com.bilibili.miniapp.open.service.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Data
public class UserAccessConfig {

    // 最近访问最大记录数
    private Integer maxRecent = 500;

    private Integer recentDays = 90;

    private String jumpUrlBase_0 = "https://miniapp.bilibili.com/applet";

    private String jumpUrlBase_1 = "https://miniapp.bilibili.com/appletx";


    // 装扮
    private String decorateAppId = "bilidecorate";

    private String decorateIcon = "https://i0.hdslb.com/bfs/activity-plat/static/20221129/33208db4c24eef2890df876c57cc4123/hAuEo89gq9.png";

    private String decorateName = "魔卡学院";

    private String decorateUrl = "https://uat-www.bilibili.com/h5/garb/card-game/home?navhide=1&f_source=plat&from=mp";

    private String decorateSummary = "一起来玩收藏集卡牌对战！";

    private List<String> needReportAppIds = List.of("bilinternal8667b8819a7e3d25");
}
