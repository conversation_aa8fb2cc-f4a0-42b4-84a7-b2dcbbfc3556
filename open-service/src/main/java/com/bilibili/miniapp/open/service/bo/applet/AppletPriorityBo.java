package com.bilibili.miniapp.open.service.bo.applet;


import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: huan
 * @Date: 2025-07-01 14:33
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppletPriorityBo {

    private MiniAppBaseInfoBo appletBaseInfo;

    private Integer priority;
}
