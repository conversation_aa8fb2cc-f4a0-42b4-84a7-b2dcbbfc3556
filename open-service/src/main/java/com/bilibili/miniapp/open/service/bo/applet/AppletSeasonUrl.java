package com.bilibili.miniapp.open.service.bo.applet;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppletSeasonUrl {
    private String appId;
    private Long seasonId;
    private Long episodeId;
    private String seasonUrl;
    private String episodeUrl;
    public static String buildSeasonKey(String appId, Long seasonId) {
        return String.format("%s_%s", appId, seasonId);
    }

    public static String buildEpisodeKey(String appId, Long seasonId, Long episodeId) {
        return String.format("%s_%s_%s", appId, seasonId, episodeId);
    }
}
