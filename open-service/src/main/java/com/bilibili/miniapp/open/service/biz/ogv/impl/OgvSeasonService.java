package com.bilibili.miniapp.open.service.biz.ogv.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bapis.pgc.servant.season.season.SearchSeasonReply;
import com.bapis.pgc.servant.season.season.SearchedSeason;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.OgvSectionType;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.service.biz.client.season.IClientSeasonUnlockService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonTabService;
import com.bilibili.miniapp.open.service.bo.client.season.ClientUserEpBo;
import com.bilibili.miniapp.open.service.bo.client.season.ClientUserSeasonBo;
import com.bilibili.miniapp.open.service.bo.common.ArchiveBo;
import com.bilibili.miniapp.open.service.bo.ogv.*;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.ShortPlayConfig;
import com.bilibili.miniapp.open.service.mapper.SeasonBizMapper;
import com.bilibili.miniapp.open.service.rpc.grpc.client.ArchiveServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.grpc.client.MainSiteAccountServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.grpc.client.PgcSeasonServiceGrpcClient;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/27 15:40
 */
@Slf4j
@Service
public class OgvSeasonService implements IOgvSeasonService {
    @Autowired
    private PgcSeasonServiceGrpcClient pgcSeasonServiceGrpcClient;
    @Autowired
    private MainSiteAccountServiceGrpcClient mainSiteAccountServiceGrpcClient;
    @Autowired
    private ArchiveServiceGrpcClient archiveServiceGrpcClient;
    @Autowired
    private IAuthorAuthorizationService authorAuthorizationService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private ISeasonTabService seasonTabService;
    @Autowired
    private IClientSeasonUnlockService clientSeasonUnlockService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSeasonToTab(String appId, int tabType, List<Long> seasonIdList) {
        if (CollectionUtils.isEmpty(seasonIdList)) {
            return;
        }
        AuthorAuthorizationBo authorization = authorAuthorizationService.getAuthorization(appId);

        Map<Long, SeasonBo> seasonMap = querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(seasonIdList)
                .appendAuthorDetail(true)
                .build());

        seasonIdList.forEach(seasonId -> {
            SeasonBo seasonBo = seasonMap.get(seasonId);
            AssertUtil.isTrue(seasonBo != null, ErrorCodeType.NO_DATA.getCode(), StrUtil.format("剧{}不存在", seasonId));
            UserInfoBo author = seasonBo.getAuthor();
            if (author != null) {
                AssertUtil.isTrue(authorization.getMidList().contains(author.getMid()), ErrorCodeType.UNAUTHORIZED.getCode(), StrUtil.format("没有得到剧{}的up主授权", seasonId));
            }
        });

        seasonTabService.addSeasonToTab(appId, tabType, seasonMap);
    }

    @Override
    public PageResult<SeasonBo> searchSeasons(Long mid, Page page, String seasonName, boolean appendEpisode) {
        SearchSeasonReply seasonReply = pgcSeasonServiceGrpcClient.querySeasons(mid, page, seasonName, GrpcCallContext.builder()
                .timeout(configCenter.getShortPlay().getSeasonTimeout())
                .build());
        if (seasonReply.getSeasonCount() == 0) {
            return PageResult.emptyPageResult();
        }
        List<Long> seasonIds = seasonReply.getSeasonList().stream()
                .map(SearchedSeason::getSeasonId)
                .collect(Collectors.toList());
        Map<Long, SeasonBo> seasonMap = querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(seasonIds)
                .appendEpisode(appendEpisode)
                .build());
        return seasonMap.isEmpty() ? PageResult.emptyPageResult() :
                PageResult.<SeasonBo>builder()
                        .total(seasonReply.getTotal())
                        .records(sortSeasonsBySeasonIdList(seasonIds, seasonMap))
                        .build();
    }

    @Override
    public EpisodeBo queryEpisodeById(Long episodeId) {
        List<EpisodeBo> episodeBos = pgcSeasonServiceGrpcClient.queryEpisodeByEpIds(List.of(episodeId), GrpcCallContext.builder()
                .timeout(configCenter.getShortPlay().getEpisodeTimeout())
                .build());
        if (CollectionUtils.isEmpty(episodeBos)) {
            log.warn("[OgvSeasonService] queryEpisodeById no data, episodeId={}", episodeId);
            return null;
        }
        return episodeBos.get(0);
    }

    @Override
    public ClientUserSeasonBo queryUserSeason(String appId, String openId, long seasonId) {
        List<Long> unlockEps = clientSeasonUnlockService.queryAllUnlockEpIds(appId, openId, seasonId);

        Map<Long, SeasonBo> seasonMap = querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(List.of(seasonId))
                .appendEpisode(true)
                .appendVideoDimension(true)
                .build());

        SeasonBo seasonBo = seasonMap.get(seasonId);
        if (seasonBo == null) {
            return null;
        }

        List<SectionBo> sections = seasonBo.getSections();
        if (CollectionUtils.isEmpty(sections)) {
            return null;
        }
        List<EpisodeBo> episodes = sections.stream()
                .map(SectionBo::getEpisodes)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(episodes)) {
            return null;
        }
        Set<Long> unlockSet = new HashSet<>(unlockEps);

        List<ClientUserEpBo> epBos = episodes.stream()
                .map(ep -> ClientUserEpBo.builder()
                        .epId(ep.getEpisodeId())
                        .order(ep.getOrd())
                        .paymentStatus(ep.getPaymentStatus())
                        .unlock(unlockSet.contains(ep.getEpisodeId()))
                        .width(ep.getWidth())
                        .height(ep.getHeight())
                        .duration(ep.getDuration())
                        .build())
                .collect(Collectors.toList());

        return ClientUserSeasonBo.builder()
                .cover(seasonBo.getCover())
                .seasonId(seasonId)
                .cover(seasonBo.getCover())
                .epList(epBos)
                .title(seasonBo.getTitle())
                .build();
    }

    @Override
    public PageResult<SeasonBo> queryTabSeasonsForClient(String appId, int tabType, Page page) {
        Map<Integer, List<Long>> allSeasonTabFromCache = seasonTabService.getAllSeasonTabFromCache(appId);
        if (CollectionUtils.isEmpty(allSeasonTabFromCache)) {
            return PageResult.emptyPageResult();
        }
        if (!allSeasonTabFromCache.containsKey(tabType)) {
            return PageResult.emptyPageResult();
        }
        List<Long> tabSeasons = allSeasonTabFromCache.get(tabType);
        if (CollectionUtils.isEmpty(tabSeasons)) {
            return PageResult.emptyPageResult();
        }

        Map<Long, SeasonBo> seasonDetailMap = querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(tabSeasons)
                .appendEpisode(true)
                .build());

        List<Long> effectiveSeasons = tabSeasons.stream()
                .filter(seasonDetailMap::containsKey)
                .collect(Collectors.toList());

        List<Long> pageSeasons = effectiveSeasons.stream()
                .sorted(Comparator.reverseOrder())
                .skip(page.getOffset())
                .limit(page.getLimit())
                .collect(Collectors.toList());

        List<SeasonBo> result = sortSeasonsBySeasonIdList(pageSeasons, seasonDetailMap);
        return PageResult.<SeasonBo>builder()
                .total(effectiveSeasons.size())
                .records(result)
                .build();
    }

    @Override
    public PageResult<SeasonBo> queryTabSeasonsForPlatform(String appId, int tabType, Page page) {
        PageResult<Long> tabResult = seasonTabService.queryTabSeasonIds(appId, tabType, page);
        if (tabResult.getTotal() == 0) {
            return PageResult.emptyPageResult();
        }

        Map<Long, SeasonBo> seasonMap = querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(tabResult.getRecords())
                .appendEpisode(true)
                .build());

        List<SeasonBo> result = sortSeasonsBySeasonIdList(tabResult.getRecords(), seasonMap);

        return PageResult.<SeasonBo>builder()
                .total(tabResult.getTotal())
                .records(result)
                .build();
    }

    private List<SeasonBo> sortSeasonsBySeasonIdList(List<Long> sortedSeasonIds, Map<Long, SeasonBo> seasonMap) {
        if (CollectionUtils.isEmpty(sortedSeasonIds)) {
            return new ArrayList<>();
        }

        List<SeasonBo> result = new ArrayList<>(sortedSeasonIds.size());
        for (Long seasonId : sortedSeasonIds) {
            SeasonBo seasonBo = seasonMap.get(seasonId);
            if (seasonBo == null) {
                continue;
            }
            result.add(seasonBo);
        }
        return result;
    }

    @Override
    public PageResult<PlatformSeasonInfoBo> querySeasons(String appId, long upMid, Page page) {

        boolean authorized = authorAuthorizationService.isAuthorized(appId, upMid);
        AssertUtil.isTrue(authorized, null, "不能查看未授权账号下内容");


        //加入校验查询不可用的season
        SearchSeasonReply searchSeasonReply = pgcSeasonServiceGrpcClient.querySeasons(upMid, page);
        if (searchSeasonReply.getSeasonCount() == 0) {
            return PageResult.emptyPageResult();
        }

        List<Long> seasonIds = searchSeasonReply.getSeasonList().stream()
                .map(SearchedSeason::getSeasonId)
                .collect(Collectors.toList());

        Map<Long, SeasonBo> seasonMap = querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(seasonIds)
                .appendEpisode(true)
                .build());

        List<SeasonBo> result = sortSeasonsBySeasonIdList(seasonIds, seasonMap);

        List<Long> alreadyPublishedSeasonIds = seasonTabService.getAlreadyPublishedSeasonIds(appId, seasonIds);

        HashSet<Long> alreadyPublishedSeasonIdSet = new HashSet<>(alreadyPublishedSeasonIds);

        return PageResult.<PlatformSeasonInfoBo>builder()
                .total(searchSeasonReply.getTotal())
                .records(result.stream()
                        .map(seasonBo -> SeasonBizMapper.MAPPER.toBo(seasonBo, alreadyPublishedSeasonIdSet))
                        .collect(Collectors.toList()))
                .build();
    }

    @Override
    public Map<Long, SeasonBo> querySeason4Short(SeasonQueryBo seasonQuery) {
        ShortPlayConfig shortPlayConfig = configCenter.getShortPlay();
        List<SeasonBo> seasonBos = pgcSeasonServiceGrpcClient.querySeason4Short(seasonQuery.getSeasonIdList(),
                GrpcCallContext.builder()
                        .timeout(shortPlayConfig.getSeasonTimeout())
                        .build());
        if (CollectionUtils.isEmpty(seasonBos)) {
            return Maps.newHashMap();
        }
        ThreadPoolExecutor executor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_OGV);
        CompletableFuture<Map<Long, List<EpisodeBo>>> episodeMapCompletableFuture = null;
        CompletableFuture<Map<Long, UserInfoBo>> authorMapCompletableFuture = null;
        if (seasonQuery.isAppendEpisode()) {
            episodeMapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                List<Long> sectionIdList = seasonBos.stream()
                        .map(SeasonBo::getSections)
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        //过滤出正片，其实pgcSeasonServiceGrpcClient.querySeason4Short本身返回的就是正片，但后期底层方法可能会放开，因此在次再次过滤出正片
                        .filter(section -> Objects.equals(section.getSectionType(), OgvSectionType.FORMAL.getCode()))
                        .map(SectionBo::getSectionId)
                        .collect(Collectors.toList());
                Map<Long, List<EpisodeBo>> episodeMap;
                try {
                    episodeMap = pgcSeasonServiceGrpcClient.queryEpisode4Short(sectionIdList, GrpcCallContext.builder()
                            .timeout(shortPlayConfig.getEpisodeTimeout()).build());
                } catch (Exception e) {
                    log.error("[OgvSeasonService] queryEpisode4Short error，query={}", JSON.toJSONString(seasonQuery), e);
                    //这个异常会体现在C端，因此如果报错则兜底为【数据不存在】
                    throw new ServiceException(ErrorCodeType.NO_DATA);
                }
                return episodeMap;
            }, executor).thenApplyAsync(episodeMap -> {
                if (episodeMap.isEmpty() || !seasonQuery.isAppendVideoDimension()) {
                    return episodeMap;
                }
                List<Long> aidList = episodeMap.values().stream()
                        .flatMap(Collection::stream)
                        .map(EpisodeBo::getAid)
                        .distinct()
                        .collect(Collectors.toList());
                try {
                    Map<Long, ArchiveBo> archiveMap = archiveServiceGrpcClient.queryArchive(aidList,
                            GrpcCallContext.builder()
                                    .timeout(shortPlayConfig.getArchiveTimeout())
                                    .build());
                    episodeMap.values().forEach(e1 -> e1.forEach(e2 -> {
                        ArchiveBo archiveBo = archiveMap.get(e2.getAid());
                        if (Objects.nonNull(archiveBo)) {
                            e2.setWidth(archiveBo.getWidth());
                            e2.setHeight(archiveBo.getHeight());
                            e2.setCover(archiveBo.getFirstFrame());
                        }
                    }));
                } catch (Exception e) {
                    //ignore
                    //宽高降级为0，客户端兜底
                    log.error("[OgvSeasonService] queryArchive error，query={}", JSON.toJSONString(seasonQuery), e);
                }
                return episodeMap;
            }, executor);
        }

        if (seasonQuery.isAppendAuthorDetail()) {
            authorMapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                List<Long> midList = null;
                try {
                    midList = seasonBos.stream().map(SeasonBo::getAuthor)
                            .filter(Objects::nonNull)
                            .map(UserInfoBo::getMid)
                            .distinct()
                            .collect(Collectors.toList());
                    return mainSiteAccountServiceGrpcClient.queryUser(midList, GrpcCallContext.builder()
                            .timeout(shortPlayConfig.getUserTimeout())
                            .build());
                } catch (Exception e) {
                    //ignore
                    //查不到则不返回作者信息，客户端兜底
                    log.error("[OgvSeasonService] queryUser error，query={},midList={}", JSON.toJSONString(seasonQuery), midList, e);
                    return Maps.newHashMap();
                }
            }, executor);
        }
        try {
            Map<Long, List<EpisodeBo>> sectionEpisodeMap = Objects.isNull(episodeMapCompletableFuture) ? Maps.newHashMap() : episodeMapCompletableFuture.get();
            Map<Long, UserInfoBo> userInfoMap = Objects.isNull(authorMapCompletableFuture) ? Maps.newHashMap() : authorMapCompletableFuture.get();
            return convertSeasonMap(seasonBos, sectionEpisodeMap, userInfoMap);
        } catch (Exception e) {
            log.error("[OgvSeasonService] querySeason4Short error，query={}", JSON.toJSONString(seasonQuery), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<Long, Boolean> queryEpisodesVisibilityByAids(List<Long> aids) {
        return pgcSeasonServiceGrpcClient.queryEpisodesVisibilityByAids(aids,
                GrpcCallContext.builder().timeout(configCenter.getShortPlay().getIsMiniAppTimeout()).build());
    }

    /**
     * 根据aid列表查询ep详情
     *
     * @param aids
     * @return
     */
    @Override
    public List<EpisodeBo> queryEpisodeByAids(List<Long> aids) {
        List<EpisodeBo> episodeBos = pgcSeasonServiceGrpcClient.queryEpisodeByAids(aids,
                GrpcCallContext.builder().timeout(configCenter.getShortPlay().getEpisodeTimeout()).build());
        if (CollectionUtils.isEmpty(episodeBos)) {
            return Collections.emptyList();
        }
        // 调整顺序和入参一致
        Map<Long, EpisodeBo> episodeBoMap = episodeBos.stream().collect(Collectors.toMap(EpisodeBo::getAid, Function.identity(), FunctionUtil.override()));
        return aids.stream().map(episodeBoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Map<Long, SeasonBo> convertSeasonMap(List<SeasonBo> seasonBos, Map<Long, List<EpisodeBo>> sectionEpisodeMap, Map<Long, UserInfoBo> userInfoMap) {
        return seasonBos.stream()
                .collect(Collectors.toMap(SeasonBo::getSeasonId,
                        seasonBo -> {
                            //如果没有查到作者信息，则使用默认的实例
                            if (Objects.nonNull(seasonBo.getAuthor()) && userInfoMap.containsKey(seasonBo.getAuthor().getMid())) {
                                seasonBo.setAuthor(userInfoMap.get(seasonBo.getAuthor().getMid()));
                            }
                            //必须分节和episode存在，才有必要循环处理
                            if (!(CollectionUtils.isEmpty(seasonBo.getSections()) || CollectionUtils.isEmpty(sectionEpisodeMap))) {
                                seasonBo.getSections().forEach(sectionBo -> sectionBo.setEpisodes(sectionEpisodeMap.get(sectionBo.getSectionId())));
                            }
                            seasonBo.setEpCount(Optional.ofNullable(seasonBo.getSections())
                                    .map(sections -> sections.stream()
                                            .mapToInt(sectionBo -> Optional.ofNullable(sectionBo.getEpisodes()).map(Collection::size).orElse(0))
                                            .sum())
                                    .orElse(0));
                            return seasonBo;
                        },
                        //duplicate key 兜底，如果重复，则以后者为准
                        (s, t) -> t));
    }
}
