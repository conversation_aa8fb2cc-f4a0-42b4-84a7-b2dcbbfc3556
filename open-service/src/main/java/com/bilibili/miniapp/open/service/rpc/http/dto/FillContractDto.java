package com.bilibili.miniapp.open.service.rpc.http.dto;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class FillContractDto {

    private String contractId;

    /**
     * 合同模板id：填写内容的jsonString
     */
    private Map<String, String> formInfos;
}
