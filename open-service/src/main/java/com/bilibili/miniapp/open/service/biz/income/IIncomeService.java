package com.bilibili.miniapp.open.service.biz.income;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;

import java.util.List;

/**
 * 收入服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface IIncomeService {

    /**
     * 获取收入汇总
     *
     * @param mid 用户ID
     * @return 收入汇总信息
     */
    IncomeSummaryBo getIncomeSummary(Long mid);

    /**
     * 获取收入明细
     *
     * @param mid            用户ID
     * @param appId          小程序ID
     * @param appName        小程序名称
     * @param beginTime      开始时间
     * @param endTime        结束时间
     * @param trafficType    流量类型
     * @param withdrawStatus 提现状态
     * @param page           页码
     * @param size           每页大小
     * @return 收入明细信息
     */
    PageResult<IncomeDetailBo> queryIncomeDetails(Long mid,
                                                  String appId,
                                                  String appName,
                                                  Long beginTime,
                                                  Long endTime,
                                                  Integer trafficType,
                                                  Integer withdrawStatus,
                                                  Integer page,
                                                  Integer size);

    void updateStatus(String appId, List<String> logDates, WithdrawStatus targetStatus);

    List<IncomeDetailBo> queryIncomeDetails(String logDate);

    void fillAllAmountIfNecessary(IncomeDetailBo detail);

    void updateAfterAccrualCompleted(List<IncomeDetailBo> incomeDetailBos);

    void unfreeze(String logDate);
}
