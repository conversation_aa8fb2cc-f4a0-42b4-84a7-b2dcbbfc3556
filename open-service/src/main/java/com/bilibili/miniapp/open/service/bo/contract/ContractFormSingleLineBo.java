package com.bilibili.miniapp.open.service.bo.contract;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * [
 *   {
 *     "name": "乙方-基本信息-联系地址",
 *     "read_only": false,
 *     "value": ""
 *   },
 *   {
 *     "name": "乙方-基本信息-联系人",
 *     "read_only": false,
 *     "value": ""
 *   }
 *   ]
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractFormSingleLineBo {
    private String name;
    private String value;
}
