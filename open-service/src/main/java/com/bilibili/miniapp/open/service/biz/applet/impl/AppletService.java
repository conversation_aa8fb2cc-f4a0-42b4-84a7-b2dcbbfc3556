package com.bilibili.miniapp.open.service.biz.applet.impl;

import com.alibaba.fastjson2.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.biz.applet.IAppletService;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationExtService;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortContext;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;
import com.bilibili.miniapp.open.service.biz.ogv.impl.SeasonAuthorizationService;
import com.bilibili.miniapp.open.service.bo.applet.*;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.bo.ogv.SectionBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.config.AppPathConfigCenter;
import com.bilibili.miniapp.open.service.mapper.MiniAppBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@Service
@Slf4j
public class AppletService implements IAppletService {

    @Resource
    private ISeasonAuthorizationExtService seasonAuthorizationExtService;

    @Resource
    private SeasonAuthorizationService seasonAuthorizationService;

    @Resource
    private MiniAppRemoteService miniAppRemoteService;

    @Resource
    private AppPathConfigCenter pathConfigCenter;

    @Resource
    private IOgvSeasonService ogvSeasonService;

    @Resource
    private IAppletUrlService appletUrlService;

    @Autowired
    private IMiniAppCustomLinkService miniAppCustomLinkService;

    @Resource
    private IAuthorAuthorizationService authorAuthorizationService;

    @Resource
    private IMiniAppService miniAppService;

    private final ThreadPoolExecutor parallelExecutor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_SHORT_PLAY);

    private final ThreadPoolExecutor refreshExecutor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_DEFAULT);

//    @Override
//    public List<AppletShortBo> getAppletShortInfoList(AppletShortContext context) {
//        StopWatch watch = new StopWatch("AppletShortInfoPipeline");
//        try {
//            List<Long> aids = context.getAids();
//            AssertUtil.isTrue(aids.size() <= 50, ErrorCodeType.BAD_PARAMETER);
//
//            // 并行查询
//            // 增加并行度监控
//            CompletableFuture<List<EpisodeBo>> episodeFuture = CompletableFuture.supplyAsync(() -> {
//                watch.start("QueryEpisodes");
//                List<EpisodeBo> episodeBos = ogvSeasonService.queryEpisodeByAids(aids);
//                watch.stop();
//                return episodeBos;
//            }, parallelExecutor);
//            CompletableFuture<Map<Long, List<SeasonAuthorizationResultBo>>> authFuture = episodeFuture.thenApplyAsync(episodeBos -> {
//                watch.start("QuerySeasonAuth");
//                if (CollectionUtils.isEmpty(episodeBos)) {
//                    return Collections.emptyMap();
//                }
//                List<Long> seasonIds = episodeBos.stream().map(EpisodeBo::getSeasonId).collect(Collectors.toList());
//                // 取出所有的season_id查询是否授权
//                Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIdsWithCache = seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
//                watch.stop();
//                return authorizedAppIdsWithCache;
//            });
//            CompletableFuture<Map<String, MiniAppBaseInfoDto>> appInfoFuture = authFuture.thenApplyAsync(longListMap -> {
//                watch.start("QueryAppInfos");
//                // 获取所有去重的appId
//                List<String> allAppIds = longListMap.values().stream().flatMap(List::stream).map(SeasonAuthorizationResultBo::getAppId).distinct().collect(Collectors.toList());
//                // 获取所有的小程序信息
//                Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(allAppIds);
//                watch.stop();
//                return appBaseInfoDtoMap;
//            });
//            return CompletableFuture.allOf(episodeFuture, authFuture, appInfoFuture)
//                    .thenApplyAsync(v -> {
//                        watch.start("AssembleResult");
//                        List<AppletShortBo> appletShortBos = assembleResult(
//                                episodeFuture.join(),
//                                authFuture.join(),
//                                appInfoFuture.join(),
//                                context.getSourceFrom()
//                        );
//                        watch.stop();
//                        return appletShortBos;
//                    }, parallelExecutor)
//                    .exceptionally(ex -> {
//                        log.error("[AppletService] getAppletShortInfoList failed", ex);
//                        return Collections.emptyList();
//                    }).join();
//        } finally {
//            log.info("[AppletService] getAppletShortInfoList Performance metrics:\n{}", watch.prettyPrint());
//        }
//    }

    @Override
    public List<AppletShortBo> getAppletShortInfoList(AppletShortContext context) {
        StopWatch watch = new StopWatch("AppletShortInfoPipeline");
        try {
            List<Long> aids = context.getAids();
            AssertUtil.isTrue(aids.size() <= 50, ErrorCodeType.BAD_PARAMETER);
            // 增加并行度监控
            // 通过avid列表查询episode列表
            watch.start("QueryEpisodes");
            List<EpisodeBo> episodeBos = ogvSeasonService.queryEpisodeByAids(aids);
            watch.stop();
            // 获取剧授权小程序
            watch.start("QuerySeasonAuth");
            Map<Long, List<SeasonAuthorizationResultBo>> seasonAuth = extractSeasonAuth(episodeBos);
            watch.stop();
            // 获取小程序信息
            watch.start("QueryAppInfos");
            Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = extractAppInfos(seasonAuth);
            watch.stop();
            // 汇总结果
            watch.start("AssembleResult");
            List<AppletShortBo> appletShortBos = assembleResult(episodeBos, seasonAuth, appBaseInfoDtoMap, context.getSourceFrom());
            watch.stop();
            return appletShortBos;
        } finally {
            log.info("[AppletService] getAppletShortInfoList Performance metrics:\n{}", watch.prettyPrint());
        }
    }

    @Override
    public AppletInfoBo getAppletInfo(AppletShortQueryParam queryParam) throws Exception {
        String appId = queryParam.getAppId();
        Long seasonId = queryParam.getSeasonId();
        Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(List.of(seasonId))
                .appendEpisode(queryParam.isAppendEpisode())
                .appendVideoDimension(queryParam.isAppendVideoDimension())
                .appendAuthorDetail(queryParam.isAppendAuthorDetail())
                .build());
        SeasonBo season = seasonBoMap.get(seasonId);
        if (season == null) {
            log.error("[AppletService] getAppletInfo failed, season not found");
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集不存在");
        }
        UserInfoBo author = season.getAuthor();
        if (author == null || !authorAuthorizationService.isAuthorized(appId, author.getMid())) {
            log.error("[AppletService] getAppletInfo failed, season not authorized");
            throw new ServiceException(ErrorCodeType.UNAUTHORIZED.getCode(), "剧集未授权");
        }
        Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(List.of(appId));
        if (MapUtils.isEmpty(appBaseInfoDtoMap)) {
            log.error("[AppletService] getAppletInfo failed, appId not found");
            throw new ServiceException(ErrorCodeType.NO_DATA);
        }
        MiniAppBaseInfoDto appBaseInfoDto = appBaseInfoDtoMap.get(appId);

        AppletCustomizedContext context = appletUrlService.getCustomizedContext(pathConfigCenter.getP(), appId);
        if (Objects.isNull(context)) {
            log.error("[AppletService] getAppletInfo failed, path context is null");
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(), "当前小程序不支持种草");
        }

        return AppletInfoBo.builder()
                .season(season)
                .appletVersion(appBaseInfoDto.getAppletVersion())
                .icon(appBaseInfoDto.getLogo())
                .title(appBaseInfoDto.getName())
                .appId(appBaseInfoDto.getAppId())
                .customizedPath(context.getPath())
                .customizedParams(context.getParams())
                .build();
    }

    @Override
    public void refreshAppletInfoCache() {
        List<String> allAuthorizedAppIds = seasonAuthorizationExtService.getAllAuthorizedAppIds();
        if (CollectionUtils.isEmpty(allAuthorizedAppIds)) {
            log.info("[AppletService] No authorized appIds found");
            return;
        }
        Lists.partition(allAuthorizedAppIds, 16).forEach(appIds -> {
            try {
                refreshExecutor.execute(() -> {
                    try {
                        miniAppRemoteService.refreshMiniAppCache(appIds);
                    } catch (Exception e) {
                        log.error("[AppletService] refreshAppletInfoCache failed. [appIds={}]", appIds, e );
                    }
                });
            } catch (Throwable ex) {
                log.error("[AppletService] refreshAppletInfoCache failed. [appIds={}]", appIds, ex);
            }
        });
    }

    @Override
    public PageResult<AppletSeasonInfoBo> getAppletSeasonInfoList(AppletSeasonQueryParam queryParam) {
        String appId = queryParam.getAppId();
        AssertUtil.hasText(appId, ErrorCodeType.BAD_PARAMETER.getCode(), "appId不能为空");
        // 获取小程序信息
        MiniAppBaseInfoBo miniAppBaseInfo = miniAppService.getMiniAppBaseInfo(appId, queryParam.getSourceFrom());
        if (miniAppBaseInfo == null) {
            log.error("[AppletService] getAppletSeasonInfoList failed, miniAppBaseInfo not found for appId: {}", appId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "小程序信息不存在");
        }
        Long mid = miniAppBaseInfo.getMid();
        PageResult<SeasonBo> seasonBoPageResult = ogvSeasonService.searchSeasons(mid, queryParam.getPage(), queryParam.getSeasonName(), queryParam.isAppendEpisode());
        int total = seasonBoPageResult.getTotal();
        List<SeasonBo> records = seasonBoPageResult.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            log.info("[AppletService] getAppletSeasonInfoList no seasons found for appId: {}", appId);
            return PageResult.emptyPageResult();
        }
        List<AppletSeasonUrlParam.SeasonEpId> seasonEpIds = new ArrayList<>();
        for (SeasonBo record : records) {
            if (!queryParam.isAppendEpisode()) {
                // 如果不需要附加剧集信息，则只需要seasonId
                AppletSeasonUrlParam.SeasonEpId seasonEpId = new AppletSeasonUrlParam.SeasonEpId();
                seasonEpId.setSeasonId(record.getSeasonId());
                seasonEpIds.add(seasonEpId);
                continue;
            }
            if (CollectionUtils.isNotEmpty(record.getSections())) {
                record.getSections().forEach(section -> {
                    if (CollectionUtils.isNotEmpty(section.getEpisodes())) {
                        section.getEpisodes().forEach(episode -> {
                            AppletSeasonUrlParam.SeasonEpId epId = new AppletSeasonUrlParam.SeasonEpId();
                            epId.setSeasonId(record.getSeasonId());
                            epId.setEpisodeId(episode.getEpisodeId());
                            seasonEpIds.add(epId);
                        });
                    } else {
                        // 如果没有剧集，则只需要seasonId
                        AppletSeasonUrlParam.SeasonEpId seasonEpId = new AppletSeasonUrlParam.SeasonEpId();
                        seasonEpId.setSeasonId(record.getSeasonId());
                        seasonEpIds.add(seasonEpId);
                    }
                });
            }
        }
        List<AppletSeasonUrl> seasonUrls = appletUrlService.getSeasonUrls(miniAppBaseInfo, seasonEpIds, queryParam.getSourceFrom());
        if (CollectionUtils.isEmpty(seasonUrls)) {
            log.info("[AppletService] getAppletSeasonInfoList no seasonUrls found for appId: {}", appId);
            return PageResult.emptyPageResult();
        }
        Map<String, String> seasonUrlMap = seasonUrls.stream().collect(Collectors.toMap(appletSeasonUrl -> AppletSeasonUrl.buildSeasonKey(appletSeasonUrl.getAppId(), appletSeasonUrl.getSeasonId()), AppletSeasonUrl::getSeasonUrl, FunctionUtil.override()));
        Map<String, String> episodeUrlMap = seasonUrls.stream().filter(seasonUrl -> NumberUtil.isPositive(seasonUrl.getEpisodeId())).collect(Collectors.toMap(appletSeasonUrl -> AppletSeasonUrl.buildEpisodeKey(appletSeasonUrl.getAppId(), appletSeasonUrl.getSeasonId(), appletSeasonUrl.getEpisodeId()), AppletSeasonUrl::getEpisodeUrl, FunctionUtil.override()));
        return new PageResult<>(total, records.stream().map(seasonBo -> seasonBoToAppletSeasonInfoBo(seasonBo, miniAppBaseInfo, seasonUrlMap, episodeUrlMap)).collect(Collectors.toList()));
    }

    @Override
    public AppletSeasonInfoBo getAppletSeasonInfo(String appId, Long seasonId, Long episodeId, Integer sourceFrom) throws Exception {
        AssertUtil.hasText(appId, ErrorCodeType.BAD_PARAMETER.getCode(), "appId不能为空");
        Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder().seasonIdList(List.of(seasonId)).build());
        SeasonBo season = seasonBoMap.get(seasonId);
        if (season == null) {
            log.error("[AppletService] getAppletInfo failed, season not found");
            return null;
        }
        UserInfoBo author = season.getAuthor();
        if (author == null || !authorAuthorizationService.isAuthorized(appId, author.getMid())) {
            log.error("[AppletService] getAppletInfo failed, season not authorized");
            throw new ServiceException(ErrorCodeType.UNAUTHORIZED.getCode(), "剧集未授权");
        }
        Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(List.of(appId));
        if (MapUtils.isEmpty(appBaseInfoDtoMap) || !appBaseInfoDtoMap.containsKey(appId)) {
            log.error("[AppletService] getAppletInfo failed, appId not found");
            throw new ServiceException(ErrorCodeType.NO_DATA);
        }
        MiniAppBaseInfoDto appBaseInfoDto = appBaseInfoDtoMap.get(appId);
        MiniAppBaseInfoBo miniAppBaseInfoBo = MiniAppBizMapper.MAPPER.toMiniAppBaseInfoBo(appBaseInfoDto);
        AppletSeasonUrl seasonUrl = appletUrlService.getSeasonUrl(miniAppBaseInfoBo, new AppletSeasonUrlParam.SeasonEpId(seasonId, episodeId), sourceFrom);
        if (seasonUrl == null) {
            log.error("[AppletService] getAppletSeasonInfo failed, seasonUrl not found for appId: {}, seasonId: {}, episodeId: {}", appId, seasonId, episodeId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集链接不存在");
        }
        AppletSeasonInfoBo.AppletSeasonInfoBoBuilder builder = AppletSeasonInfoBo.builder()
                .miniAppBaseInfo(miniAppBaseInfoBo)
                .seasonId(season.getSeasonId())
                .cover(season.getCover())
                .linkUrl(seasonUrl.getSeasonUrl())
                .title(season.getTitle());
        if (NumberUtil.isPositive(episodeId)) {
            // 如果指定了episodeId，则获取对应的剧集信息
            EpisodeBo episodeBo = ogvSeasonService.queryEpisodeById(episodeId);
            if (episodeBo == null || !episodeBo.getSeasonId().equals(seasonId)) {
                log.error("[AppletService] getAppletSeasonInfo failed, episode not found for appId: {}, seasonId: {}, episodeId: {}", appId, seasonId, episodeId);
                throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集不存在");
            }
            builder.episodes(List.of(AppletEpisodeInfoBo.builder()
                    .episodeId(episodeBo.getEpisodeId())
                    .title(episodeBo.getTitle())
                    .cover(episodeBo.getCover())
                    .longTitle(episodeBo.getLongTitle())
                    .linkUrl(seasonUrl.getEpisodeUrl())
                    .build()));
        }
        return builder.build();
    }

    private AppletSeasonInfoBo seasonBoToAppletSeasonInfoBo(SeasonBo seasonBo, MiniAppBaseInfoBo miniAppBaseInfoBo, Map<String, String> seasonUrlMap, Map<String, String> episodeUrlMap) {
        String appId = miniAppBaseInfoBo.getAppId();
        String linkUrl = seasonUrlMap.get(AppletSeasonUrl.buildSeasonKey(appId, seasonBo.getSeasonId()));
        AppletSeasonInfoBo.AppletSeasonInfoBoBuilder builder = AppletSeasonInfoBo.builder()
                .seasonId(seasonBo.getSeasonId())
                .title(seasonBo.getTitle())
                .linkUrl(linkUrl)
                .miniAppBaseInfo(miniAppBaseInfoBo)
                .cover(seasonBo.getCover());
        List<SectionBo> sections = seasonBo.getSections();
        if (CollectionUtils.isNotEmpty(sections)) {
            List<AppletEpisodeInfoBo> episodes = sections.stream()
                    .filter(section -> CollectionUtils.isNotEmpty(section.getEpisodes()))
                    .flatMap(section -> section.getEpisodes().stream())
                    .map(episode -> AppletEpisodeInfoBo.builder()
                            .episodeId(episode.getEpisodeId())
                            .title(episode.getTitle())
                            .cover(episode.getCover())
                            .longTitle(episode.getLongTitle())
                            .linkUrl(episodeUrlMap.get(AppletSeasonUrl.buildEpisodeKey(appId, seasonBo.getSeasonId(), episode.getEpisodeId())))
                            .build())
                    .collect(Collectors.toList());
            builder.episodes(episodes);
        }
        return builder.build();
    }

    private List<AppletShortBo> assembleResult(List<EpisodeBo> episodeBoList,
                                               Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIds,
                                               Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap,
                                               Integer sourceFrom) {
        StopWatch stopWatch = new StopWatch("AssembleResult");
        // 构建返回结果
        List<AppletShortBo> result = Lists.newArrayList();
        // 获取所有的appId
        Set<String> appIds = appBaseInfoDtoMap.keySet();
        Map<String, AppletCustomizedContext> customizedContextMap = appletUrlService.getCustomizedContextMap(new ArrayList<>(appIds));
        for (EpisodeBo value : episodeBoList) {
            stopWatch.start("BuildAppletShortBo-" + value.getAid());
            AppletShortBo.AppletShortBoBuilder builder = AppletShortBo.builder();
            builder.seasonId(value.getSeasonId());
            builder.episodeId(value.getEpisodeId());
            builder.aid(value.getAid());
            List<SeasonAuthorizationResultBo> seasonAuthInfos = authorizedAppIds.get(value.getSeasonId());
            if (CollectionUtils.isEmpty(seasonAuthInfos)) {
                // 没有授权信息 直接跳过
                continue;
            } else {
                builder.status(AppletShortBo.Status.builder().isMiniApp(true).build());
                List<AppletShortBo.Candidate> candidates = seasonAuthInfos.stream().map(info -> {
                    String appId = info.getAppId();
                    MiniAppBaseInfoDto baseInfoDto = appBaseInfoDtoMap.get(appId);
                    String jumpUrl = appletUrlService.buildJumpUrl(customizedContextMap.get(appId), appId, baseInfoDto.getAppletVersion(), value.getSeasonId(), value.getEpisodeId(), sourceFrom);
//                    String jumpUrl = buildJumpUrl(baseInfoDto, value.getSeasonId(), value.getEpisodeId(), sourceFrom);
                    if (StringUtils.isBlank(jumpUrl)) {
                        return null;
                    }
                    return AppletShortBo.Candidate.builder()
                            .appId(appId)
                            .onlineStatus(Integer.valueOf(baseInfoDto.getOffline()))
                            .authTime(info.getAuthTime())
                            .defaultTag(info.isDefaultTag())
                            .jumpUrl(jumpUrl)
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());
                builder.candidates(candidates);
            }
            result.add(builder.build());
            stopWatch.stop();
        }
        log.info("[AppletService] assembleResult Performance metrics:\n{}", stopWatch.prettyPrint());
        return result;
    }

    private Map<String, MiniAppBaseInfoDto> extractAppInfos(Map<Long, List<SeasonAuthorizationResultBo>> longListMap) {
        // 获取所有去重的appId
        List<String> allAppIds = longListMap.values().stream().flatMap(List::stream).map(SeasonAuthorizationResultBo::getAppId).distinct().collect(Collectors.toList());
        // 获取所有的小程序信息
        return miniAppRemoteService.queryAppInfosWithinCache(allAppIds);
    }

    private Map<Long, List<SeasonAuthorizationResultBo>> extractSeasonAuth(List<EpisodeBo> episodeBos) {
        if (CollectionUtils.isEmpty(episodeBos)) {
            return Collections.emptyMap();
        }
        List<Long> seasonIds = episodeBos.stream().map(EpisodeBo::getSeasonId).collect(Collectors.toList());

        // 取出所有的season_id查询是否授权
        return seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
    }
}
