package com.bilibili.miniapp.open.service.biz.applet.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.biz.applet.IAppletService;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationExtService;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortContext;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.biz.ogv.impl.SeasonAuthorizationService;
import com.bilibili.miniapp.open.service.bo.applet.*;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.config.AppPathConfigCenter;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@Service
@Slf4j
public class AppletService implements IAppletService {

    @Resource
    private ISeasonAuthorizationExtService seasonAuthorizationExtService;

    @Resource
    private SeasonAuthorizationService seasonAuthorizationService;

    @Resource
    private MiniAppRemoteService miniAppRemoteService;

    @Resource
    private AppPathConfigCenter pathConfigCenter;

    @Resource
    private IOgvSeasonService ogvSeasonService;

    @Resource
    private IAppletUrlService appletUrlService;

    @Autowired
    private IMiniAppCustomLinkService miniAppCustomLinkService;

    @Resource
    private IAuthorAuthorizationService authorAuthorizationService;

    @Resource
    private IMiniAppService miniAppService;

    private final ThreadPoolExecutor parallelExecutor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_SHORT_PLAY);

    private final ThreadPoolExecutor refreshExecutor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_DEFAULT);
    @Autowired
    private AppletSeasonService appletSeasonService;

//    @Override
//    public List<AppletShortBo> getAppletShortInfoList(AppletShortContext context) {
//        StopWatch watch = new StopWatch("AppletShortInfoPipeline");
//        try {
//            List<Long> aids = context.getAids();
//            AssertUtil.isTrue(aids.size() <= 50, ErrorCodeType.BAD_PARAMETER);
//
//            // 并行查询
//            // 增加并行度监控
//            CompletableFuture<List<EpisodeBo>> episodeFuture = CompletableFuture.supplyAsync(() -> {
//                watch.start("QueryEpisodes");
//                List<EpisodeBo> episodeBos = ogvSeasonService.queryEpisodeByAids(aids);
//                watch.stop();
//                return episodeBos;
//            }, parallelExecutor);
//            CompletableFuture<Map<Long, List<SeasonAuthorizationResultBo>>> authFuture = episodeFuture.thenApplyAsync(episodeBos -> {
//                watch.start("QuerySeasonAuth");
//                if (CollectionUtils.isEmpty(episodeBos)) {
//                    return Collections.emptyMap();
//                }
//                List<Long> seasonIds = episodeBos.stream().map(EpisodeBo::getSeasonId).collect(Collectors.toList());
//                // 取出所有的season_id查询是否授权
//                Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIdsWithCache = seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
//                watch.stop();
//                return authorizedAppIdsWithCache;
//            });
//            CompletableFuture<Map<String, MiniAppBaseInfoDto>> appInfoFuture = authFuture.thenApplyAsync(longListMap -> {
//                watch.start("QueryAppInfos");
//                // 获取所有去重的appId
//                List<String> allAppIds = longListMap.values().stream().flatMap(List::stream).map(SeasonAuthorizationResultBo::getAppId).distinct().collect(Collectors.toList());
//                // 获取所有的小程序信息
//                Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(allAppIds);
//                watch.stop();
//                return appBaseInfoDtoMap;
//            });
//            return CompletableFuture.allOf(episodeFuture, authFuture, appInfoFuture)
//                    .thenApplyAsync(v -> {
//                        watch.start("AssembleResult");
//                        List<AppletShortBo> appletShortBos = assembleResult(
//                                episodeFuture.join(),
//                                authFuture.join(),
//                                appInfoFuture.join(),
//                                context.getSourceFrom()
//                        );
//                        watch.stop();
//                        return appletShortBos;
//                    }, parallelExecutor)
//                    .exceptionally(ex -> {
//                        log.error("[AppletService] getAppletShortInfoList failed", ex);
//                        return Collections.emptyList();
//                    }).join();
//        } finally {
//            log.info("[AppletService] getAppletShortInfoList Performance metrics:\n{}", watch.prettyPrint());
//        }
//    }

    @Override
    public List<AppletShortBo> getAppletShortInfoList(AppletShortContext context) {
        List<Long> aids = context.getAids();
        AssertUtil.isTrue(aids.size() <= 50, ErrorCodeType.BAD_PARAMETER);
        // 增加并行度监控
        // 通过avid列表查询episode列表
        List<EpisodeBo> episodeBos = ogvSeasonService.queryEpisodeByAids(aids);
        List<Long> seasonIds = extractSeasonIds(episodeBos);
        // 获取剧授权小程序
        Map<Long, String> cachedApplets = appletSeasonService.getDefaultAppletBySeasonIdsFromCache(seasonIds);
        List<String> appletPriority = appletSeasonService.getAppletPriorityFromCache();
        Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIds = seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
        List<String> allAppIds = extractAppIds(authorizedAppIds);
        List<MiniAppBaseInfoBo> appBaseInfoBos = fetchAppBaseInfo(allAppIds, context.getSourceFrom());
        Map<String, AppletCustomizedContext> contextMap = appletUrlService.getCustomizedContextMap(allAppIds);
        // 获取season_id绑定的小程序
        Map<Long, String> defaultApplets = getDefaultAppletBySeasonIds(seasonIds, cachedApplets, appletPriority, authorizedAppIds, appBaseInfoBos, contextMap);
        // 汇总结果
        return assembleResult(episodeBos, appBaseInfoBos, defaultApplets, contextMap, context.getSourceFrom());
    }

    @Override
    public AppletInfoBo getAppletInfo(AppletShortQueryParam queryParam) throws Exception {
        String appId = queryParam.getAppId();
        Long seasonId = queryParam.getSeasonId();
        Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(List.of(seasonId))
                .appendEpisode(queryParam.isAppendEpisode())
                .appendVideoDimension(queryParam.isAppendVideoDimension())
                .appendAuthorDetail(queryParam.isAppendAuthorDetail())
                .build());
        SeasonBo season = seasonBoMap.get(seasonId);
        if (season == null) {
            log.error("[AppletService] getAppletInfo failed, season not found");
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集不存在");
        }
        UserInfoBo author = season.getAuthor();
        if (author == null || !authorAuthorizationService.isAuthorized(appId, author.getMid())) {
            log.error("[AppletService] getAppletInfo failed, season not authorized");
            throw new ServiceException(ErrorCodeType.UNAUTHORIZED.getCode(), "剧集未授权");
        }
        Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(List.of(appId));
        if (MapUtils.isEmpty(appBaseInfoDtoMap)) {
            log.error("[AppletService] getAppletInfo failed, appId not found");
            throw new ServiceException(ErrorCodeType.NO_DATA);
        }
        MiniAppBaseInfoDto appBaseInfoDto = appBaseInfoDtoMap.get(appId);

        AppletCustomizedContext context = appletUrlService.getCustomizedContext(pathConfigCenter.getP(), appId);
        if (Objects.isNull(context)) {
            log.error("[AppletService] getAppletInfo failed, path context is null");
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(), "当前小程序不支持种草");
        }

        return AppletInfoBo.builder()
                .season(season)
                .appletVersion(appBaseInfoDto.getAppletVersion())
                .icon(appBaseInfoDto.getLogo())
                .title(appBaseInfoDto.getName())
                .appId(appBaseInfoDto.getAppId())
                .customizedPath(context.getPath())
                .customizedParams(context.getParams())
                .build();
    }

    @Override
    public void refreshAppletInfoCache() {
        List<String> allAuthorizedAppIds = seasonAuthorizationExtService.getAllAuthorizedAppIds();
        if (CollectionUtils.isEmpty(allAuthorizedAppIds)) {
            log.info("[AppletService] No authorized appIds found");
            return;
        }
        Lists.partition(allAuthorizedAppIds, 16).forEach(appIds -> {
            try {
                refreshExecutor.execute(() -> {
                    try {
                        miniAppRemoteService.refreshMiniAppCache(appIds);
                    } catch (Exception e) {
                        log.error("[AppletService] refreshAppletInfoCache failed. [appIds={}]", appIds, e );
                    }
                });
            } catch (Throwable ex) {
                log.error("[AppletService] refreshAppletInfoCache failed. [appIds={}]", appIds, ex);
            }
        });
    }

    @Override
    public Map<Long, String> getDefaultAppletBySeasonIds(List<Long> seasonIds, Integer sourceFrom) {
        if (CollectionUtils.isEmpty(seasonIds)) {
            return Collections.emptyMap();
        }
        Map<Long, String> cachedApplets = appletSeasonService.getDefaultAppletBySeasonIdsFromCache(seasonIds);
        List<String> appletPriority = appletSeasonService.getAppletPriorityFromCache();
        Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIds = seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
        List<String> appIds = extractAppIds(authorizedAppIds);
        List<MiniAppBaseInfoBo> appBaseInfoBos = fetchAppBaseInfo(appIds, sourceFrom);
        Map<String, AppletCustomizedContext> contextMap = appletUrlService.getCustomizedContextMap(appIds);
        return getDefaultAppletBySeasonIds(seasonIds, cachedApplets, appletPriority, authorizedAppIds, appBaseInfoBos, contextMap);
    }


    private Map<Long, String> getDefaultAppletBySeasonIds(List<Long> seasonIds,
                                                          Map<Long, String> cachedApplets,
                                                          List<String> appletPriority,
                                                          Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIds,
                                                          List<MiniAppBaseInfoBo> appBaseInfoBos,
                                                          Map<String, AppletCustomizedContext> contextMap) {
        // 1. 先查询season_id绑定的小程序
        if (CollectionUtils.isEmpty(seasonIds)) {
            return Collections.emptyMap();
        }
        Map<String, MiniAppBaseInfoBo> appBaseInfoBoMap = appBaseInfoBos.stream().collect(Collectors.toMap(MiniAppBaseInfoBo::getAppId, Function.identity(), FunctionUtil.override()));
        Map<Long, String> result = new HashMap<>();
        for (Long seasonId : seasonIds) {
            // 判断是否绑定小程序
            String appId = cachedApplets.get(seasonId);
            if (StringUtils.isNotBlank(appId)) {
                // 如果绑定了小程序，直接返回
                result.put(seasonId, appId);
                continue;
            }
            // 如果没有绑定小程序，查询授权的小程序列表
            List<SeasonAuthorizationResultBo> seasonAuthInfos = authorizedAppIds.get(seasonId);
            if (CollectionUtils.isEmpty(seasonAuthInfos)) {
                // 没有授权信息，直接跳过
                continue;
            }
            SeasonAuthorizationResultBo authorization = seasonAuthorizationExtService.getDefaultAuthorization(seasonAuthInfos, appletPriority);
            if (Objects.nonNull(authorization) && Objects.nonNull(appBaseInfoBoMap.get(authorization.getAppId())) && Objects.nonNull(contextMap.get(authorization.getAppId()))) {
                result.put(seasonId, authorization.getAppId());
            }
        }
        return result;
    }

    private List<AppletShortBo> assembleResult(List<EpisodeBo> episodeBoList,
                                               List<MiniAppBaseInfoBo> appBaseInfoBos,
                                               Map<Long, String> seasonAppletMap,
                                               Map<String, AppletCustomizedContext> customizedContextMap,
                                               Integer sourceFrom) {
        // 构建返回结果
        List<AppletShortBo> result = Lists.newArrayList();
        // 获取所有的appId
        Map<String, MiniAppBaseInfoBo> appBaseInfoBoMap = appBaseInfoBos.stream().collect(Collectors.toMap(MiniAppBaseInfoBo::getAppId, Function.identity(), FunctionUtil.override()));
        for (EpisodeBo value : episodeBoList) {
            AppletShortBo.AppletShortBoBuilder builder = AppletShortBo.builder();
            Long seasonId = value.getSeasonId();
            String appId = seasonAppletMap.get(seasonId);
            if (StringUtils.isBlank(appId)) {
                // 没有绑定小程序，直接跳过
                log.warn("[AppletService] assembleResult appId is empty. [seasonId={}]", seasonId);
                continue;
            }
            MiniAppBaseInfoBo appBaseInfoBo = appBaseInfoBoMap.get(appId);
            if (appBaseInfoBo == null) {
                log.warn("[AppletService] assembleResult appBaseInfoBo is null. [appId={}]", appId);
                continue;
            }
            builder.seasonId(seasonId);
            builder.episodeId(value.getEpisodeId());
            builder.aid(value.getAid());
            builder.jumpUrl(appletUrlService.buildJumpUrl(customizedContextMap.get(appId), appId, appBaseInfoBo.getAppletVersion(), seasonId, value.getEpisodeId(), sourceFrom));
            builder.appId(appId);
            result.add(builder.build());
        }
        return result;
    }

    private List<Long> extractSeasonIds(List<EpisodeBo> episodeBos) {
        if (CollectionUtils.isEmpty(episodeBos)) {
            return Collections.emptyList();
        }
        return episodeBos.stream().map(EpisodeBo::getSeasonId).distinct().collect(Collectors.toList());
    }

    private List<String> extractAppIds(Map<Long, List<SeasonAuthorizationResultBo>> seasonAuthInfos) {
        if (MapUtils.isEmpty(seasonAuthInfos)) {
            return Collections.emptyList();
        }
        return seasonAuthInfos.values().stream()
                .flatMap(Collection::stream)
                .map(SeasonAuthorizationResultBo::getAppId)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<MiniAppBaseInfoBo> fetchAppBaseInfo(List<String> appIds, Integer sourceFrom) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyList();
        }
        return miniAppService.getMiniAppBaseInfoListFromCache(appIds, sourceFrom);
    }
}
