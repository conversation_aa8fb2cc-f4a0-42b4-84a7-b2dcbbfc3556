package com.bilibili.miniapp.open.service.rpc.grpc.server;

import com.bapis.ad.applet.Pageable;
import com.bapis.ad.applet.platform.season.*;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.biz.applet.IAppletService;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonQueryParam;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.server.delegate.AppletSeasonGrpcServiceDelegate;
import io.grpc.stub.StreamObserver;
import org.apache.commons.collections4.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@RPCService
public class AppletSeasonGrpcService extends AppletSeasonServiceGrpc.AppletSeasonServiceImplBase {

    @Resource
    private AppletSeasonGrpcServiceDelegate seasonGrpcServiceDelegate;

    @Override
    public void queryAppletSeasonInfo(QueryAppletSeasonInfoRequest request, StreamObserver<GetAppletSeasonInfoResponse> responseObserver) {
        // 使用委托类处理请求
        seasonGrpcServiceDelegate.queryAppletSeasonInfo(request, responseObserver);
    }

    @Override
    public void getAppletSeasonInfoById(GetAppletSeasonInfoByIdRequest request, StreamObserver<GetAppletSeasonInfoByIdResponse> responseObserver) {
        seasonGrpcServiceDelegate.getAppletSeasonInfoById(request, responseObserver);
    }
}
