package com.bilibili.miniapp.open.service.biz.ogv;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAuthorAuthorizationDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonAuthorizationExtDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAuthorAuthorizationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAuthorAuthorizationPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/

@Service
@Slf4j
public class SeasonAuthorizationExtService implements ISeasonAuthorizationExtService {

    @Resource
    private MiniAppOpenSeasonAuthorizationExtDao miniAppOpenSeasonAuthorizationExtDao;

    @Resource
    private ConfigCenter configCenter;

    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;

    @Resource
    private IOgvSeasonService ogvSeasonService;

    @Resource
    private MiniAppOpenAuthorAuthorizationDao authorAuthorizationDao;

    @Override
    public Map<Long, List<SeasonAuthorizationResultBo>> getAuthorizedAppIdsFromDb(List<Long> seasonIds) {
        StopWatch stopWatch = new StopWatch("SeasonAuthorizationExtService.getAuthorizedAppIdsFromDb");
        try {
            stopWatch.start("querySeason4Short");
            Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                    .seasonIdList(seasonIds)
                    .build());
            stopWatch.stop();
            // 取出这些剧的mid
            List<Long> mids = seasonBoMap.values().stream()
                    .map(SeasonBo::getAuthor)
                    .filter(Objects::nonNull)
                    .map(UserInfoBo::getMid)
                    .filter(mid -> Objects.nonNull(mid) && mid > 0)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mids)) {
                return Collections.emptyMap();
            }
            MiniAppOpenAuthorAuthorizationPoExample example = new MiniAppOpenAuthorAuthorizationPoExample();
            example.createCriteria().andMidIn(mids).andStatusEqualTo(1).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            stopWatch.start("queryAuthorAuthorization");
            List<MiniAppOpenAuthorAuthorizationPo> pos = authorAuthorizationDao.selectByExample(example);
            stopWatch.stop();
            if (CollectionUtils.isEmpty(pos)) {
                return Collections.emptyMap();
            }
            Map<Long, List<SeasonAuthorizationResultBo>> result = new HashMap<>();
            seasonBoMap.forEach((seasonId, seasonBo) -> {
                if (seasonBo.getAuthor() != null && seasonBo.getAuthor().getMid() != null && seasonBo.getAuthor().getMid() > 0) {
                    List<MiniAppOpenAuthorAuthorizationPo> filtered = pos.stream()
                            .filter(po -> po.getMid().equals(seasonBo.getAuthor().getMid()))
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filtered)) {
                        List<SeasonAuthorizationResultBo> resultList = filtered.stream()
                                .map(po -> {
                                    SeasonAuthorizationResultBo resultBo = new SeasonAuthorizationResultBo();
                                    resultBo.setSeasonId(seasonId);
                                    resultBo.setAppId(po.getAppId());
                                    resultBo.setAuthTime(po.getMtime());
                                    resultBo.setSeasonMid(po.getMid());
                                    return resultBo;
                                })
                                .collect(Collectors.toList());
                        result.put(seasonId, resultList);
                    }
                }
            });
            return result;
        } catch (Exception e) {
            log.error("[SeasonAuthorizationExtService] extractSeasonAuth Failed to query season info", e);
            return Collections.emptyMap();
        } finally {
            log.info("[SeasonAuthorizationExtService] getAuthorizedAppIdsFromDb seasonIds={} cost={}",
                    seasonIds, stopWatch.prettyPrint());
        }
    }

    public Map<Long, List<SeasonAuthorizationResultBo>>
    getAuthorizedAppIdsWithCache(List<Long> seasonIds) {
        Map<Long, List<SeasonAuthorizationResultBo>> results = new HashMap<>();
        List<Long> missedIds = new ArrayList<>();
        // Redis缓存查询
        Set<Long> AllSeasonIds = new HashSet<>(seasonIds);
        for (Long sid : AllSeasonIds) {
            String key = RedisKeyPattern.buildAppSeasonAppletKey(sid);
            List<SeasonAuthorizationResultBo> cached = cacheRepository.getList(key, SeasonAuthorizationResultBo.class);
            if (!CollectionUtils.isEmpty(cached)) {
                results.put(sid, cached);
            } else {
                missedIds.add(sid);
            }
        }
        if (CollectionUtils.isEmpty(missedIds)) {
            // 全部走缓存了，很好
            return results;
        }
        // 数据库查询
        Map<Long, List<SeasonAuthorizationResultBo>> dbResults = getAuthorizedAppIdsFromDb(missedIds);
        // 保存到缓存
        dbResults.forEach((sid, list) -> {
            String key = RedisKeyPattern.buildAppSeasonAppletKey(sid);
            cacheRepository.addAllList(key, list, configCenter.getShortPlay().getSeasonAuthCacheTime(), TimeUnit.MINUTES);
            results.put(sid, list);
        });
        return results;
    }

    @Override
    public void warmUpCache(List<Long> seasonIds) {
        Map<Long, List<SeasonAuthorizationResultBo>> dbResults = getAuthorizedAppIdsFromDb(seasonIds);
        dbResults.forEach((sid, list) -> {
            String key = RedisKeyPattern.buildAppSeasonAppletKey(sid);
            cacheRepository.addAllList(key, list, configCenter.getShortPlay().getSeasonAuthCacheTime(), TimeUnit.MINUTES);
        });
    }

    @Override
    public List<String> getAllAuthorizedAppIds() {
        return miniAppOpenSeasonAuthorizationExtDao.selectDistinctAppIds();
    }

    @Override
    public Map<Long, List<SeasonAuthorizationResultBo>> getAppIdsByMidIn(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Map.of();
        }
        MiniAppOpenAuthorAuthorizationPoExample example = new MiniAppOpenAuthorAuthorizationPoExample();
        example.createCriteria().andMidIn(mids).andStatusEqualTo(1).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenAuthorAuthorizationPo> list = authorAuthorizationDao.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Map.of();
        }
        return list.stream()
                .map(po -> {
                    SeasonAuthorizationResultBo resultBo = new SeasonAuthorizationResultBo();
                    resultBo.setAppId(po.getAppId());
                    resultBo.setSeasonMid(po.getMid());
                    resultBo.setAuthTime(po.getMtime());
                    return resultBo;
                }).collect(Collectors.groupingBy(SeasonAuthorizationResultBo::getSeasonMid));

    }

    @Override
    public List<Long> getAllAuthorizedMids() {
        return miniAppOpenSeasonAuthorizationExtDao.selectDistinctMids();
    }

    @Override
    public SeasonAuthorizationResultBo getDefaultAuthorization(List<SeasonAuthorizationResultBo> authResults, List<String> appletPriority) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(authResults)) {
            return null; // 没有授权结果，返回null
        }
        // 如果只有一个授权结果，则直接返回
        if (authResults.size() == 1) {
            return authResults.get(0);
        }
        // 按照优先级设置默认结果
        Map<String, SeasonAuthorizationResultBo> authorizationResultBoMap = authResults.stream().collect(Collectors.toMap(SeasonAuthorizationResultBo::getAppId, Function.identity(), FunctionUtil.override()));
        for (String appId : appletPriority) {
            if (authorizationResultBoMap.containsKey(appId)) {
                return authorizationResultBoMap.get(appId);
            }
        }
        return authResults.stream().filter(SeasonAuthorizationResultBo::isDefaultTag)
                .findFirst()
                .orElse(authResults.stream().min(Comparator.comparing(SeasonAuthorizationResultBo::getAuthTime)).orElse(null));
    }
}
