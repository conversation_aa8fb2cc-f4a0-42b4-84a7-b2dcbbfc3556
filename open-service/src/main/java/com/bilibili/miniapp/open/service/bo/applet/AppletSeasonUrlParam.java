package com.bilibili.miniapp.open.service.bo.applet;

import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppletSeasonUrlParam {
    private List<MiniAppBaseInfoBo> appletBaseInfos;
    private Integer sourceFrom;
    private SeasonEpIds seasonEpId;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SeasonEpIds {
        private Long seasonId;
        private List<Long> episodeIds = new ArrayList<>();
    }
}
