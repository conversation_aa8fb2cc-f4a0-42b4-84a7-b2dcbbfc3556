package com.bilibili.miniapp.open.service.bo.applet;

import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppletSeasonUrlParam {
    private List<MiniAppBaseInfoBo> appletBaseInfos;
    private Integer sourceFrom;
    private SeasonEpId seasonEpId;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SeasonEpId {
        private Long seasonId;
        private Long episodeId;
    }
}
