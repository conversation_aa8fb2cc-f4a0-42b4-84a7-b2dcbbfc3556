package com.bilibili.miniapp.open.service.bo.applet;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppletShortBo {

    private Long seasonId;

    private Long episodeId;

    private Long aid;

    private Status status;

    private String jumpUrl;

    private String appId;

    private List<Candidate> candidates;

    private List<MiniAppBaseInfoDto> candidateApps;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Status {
        // 是否小程序
        private boolean isMiniApp;
        // 可见场景白名单
        private List<Long> visibleScenes;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Candidate {
        // 小程序唯一标识
        private String appId;
        // 上下线状态 0-上线 1-下线
        private Integer onlineStatus;
        // 是否默认标签
        private boolean defaultTag;
        // 授权时间
        private Timestamp authTime;
        // 完整跳转链接
        private String jumpUrl;
    }
}
