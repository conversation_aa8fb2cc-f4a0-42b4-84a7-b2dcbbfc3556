package com.bilibili.miniapp.open.service.mapper;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.applet.platform.company.AppInfo;
import com.bapis.ad.applet.platform.company.AppletEnterpriseInfo;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyDetailDto;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCompanyAdmissionPo;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditListBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyInfoBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/3/3
 */
@Mapper
public interface CompanyBizMapper {
    CompanyBizMapper MAPPER = Mappers.getMapper(CompanyBizMapper.class);

    @Mapping(target = "companyInfo", source = "editInfo")
    CompanyDetailBo toBo(MiniAppOpenCompanyAdmissionPo po);

    @Mapping(target = "mid", ignore = true)
    @Mapping(target = "operatorName", source = "contact")
    @Mapping(target = "creditCode", source = "code")
    @Mapping(target = "contactEmail", source = "email")
    @Mapping(target = "businessLicense", source = "license")
    CompanyInfoBo toCompanyInfoBo(MiniAppCompanyDetailDto companyDetail);


    @Mapping(target = "createTime", source = "mtime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    CompanyAdmissionAuditListBo toAuditListBo(MiniAppOpenCompanyAdmissionPo po);

    @Mapping(target = "createTime", source = "mtime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "admissionInfo", source = "editInfo")
    CompanyAdmissionAuditInfoBo toAuditInfoBo(MiniAppOpenCompanyAdmissionPo admission);

    AppletEnterpriseInfo toEnterpriseInfo(CompanyInfoBo companyInfo);

    default String map(CompanyInfoBo companyInfo) {
        return JSON.toJSONString(companyInfo);
    }

    default CompanyInfoBo map(String editInfo) {
        return JSON.parseObject(editInfo, CompanyInfoBo.class);
    }

    default AppCompanyAuditStatus map(Integer auditStatus) {
        return AppCompanyAuditStatus.getByCode(auditStatus);
    }


}
