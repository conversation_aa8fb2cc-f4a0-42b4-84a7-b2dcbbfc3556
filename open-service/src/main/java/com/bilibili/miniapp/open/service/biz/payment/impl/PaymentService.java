package com.bilibili.miniapp.open.service.biz.payment.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.bilibili.miniapp.open.common.entity.BiliPayResponse;
import com.bilibili.miniapp.open.common.entity.LockOption;
import com.bilibili.miniapp.open.common.entity.TracePropertyKey;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.*;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRefundDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRefundExtraDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPoExample;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.order.IOrderService;
import com.bilibili.miniapp.open.service.biz.payment.IPaymentService;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import com.bilibili.miniapp.open.service.bo.order.*;
import com.bilibili.miniapp.open.service.bo.payment.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.OrderConfig;
import com.bilibili.miniapp.open.service.config.PaymentConfig;
import com.bilibili.miniapp.open.service.eventbus.IOpenEventPublisher;
import com.bilibili.miniapp.open.service.eventbus.OpenEvent;
import com.bilibili.miniapp.open.service.eventbus.OpenEventPublisher;
import com.bilibili.miniapp.open.service.eventbus.event.OpenOrderPayNotifyEvent;
import com.bilibili.miniapp.open.service.eventbus.event.OpenRefundEvent;
import com.bilibili.miniapp.open.service.mapper.PaymentMapper;
import com.bilibili.miniapp.open.service.rpc.http.IPayPlatformAccessLayerRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.IPayPlatformCashierRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.IPayPlatformRemoteService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.breaker.exception.BiliBreakerRejectedException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 支付中台文档
 * <p>
 * 1、<a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957974">现金：支付</a>
 * <p>
 * 2、<a href="https://info.bilibili.co/pages/viewpage.action?pageId=846771222">B币：发起充值并消费</a>
 *
 * <AUTHOR>
 * @date 2025/1/16 16:59
 */
@Slf4j
@Service
public class PaymentService extends AbstractOpenService implements IPaymentService {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IPayPlatformCashierRemoteService payPlatformCashierRemoteService;
    @Autowired
    private IPayPlatformAccessLayerRemoteService payPlatformAccessLayerRemoteService;
    @Autowired
    private IPayPlatformRemoteService payPlatformRemoteService;
    @Autowired
    private ConfigCenter configCenter;
    @Resource(type = OpenEventPublisher.class)
    private IOpenEventPublisher<OpenEvent> openEventPublisher;
    @Autowired
    private MiniAppOpenRefundDao openOrderRefundDao;
    @Autowired
    private MiniAppOpenRefundExtraDao openOrderRefundExtraDao;
    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;
    @Autowired
    private IOpenAccessService openAccessService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ExchangePayInfo exchangePayInfo(ExchangePayInfoReq req) throws Exception {
        ExchangeValidationContext validationContext = validateExchange(req);
        Order order = validationContext.getOrder();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), order.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在进行中，请勿重复操作")
                    .build());

            PlatformType platformType = validationContext.getPlatformType();
            ExchangePayInfo exchangePayInfo = getExchangePayInfoPay(req, validationContext);
            OrderConfig orderConfig = configCenter.getOrder();
            OrderExtra orderExtra = OrderExtra.builder()
                    .payParamInfo(exchangePayInfo.getPayParam().toJSONString())
                    .traceInfo(validationContext.getTraceInfo().toJSONString())
                    .build();
            Order updateOrder = Order.builder()
                    .orderId(order.getOrderId())
                    .platform(platformType.getCode())
                    .sourceChannel(validationContext.getSourceChannel().getCode())
                    //https://doc.weixin.qq.com/doc/w3_AGEACAbzADgMxHpg6T8Q0ioC2uHHr?scode=ANYAEAdoABET3bjqtJAVAAMQZxACU
                    //自然流量存在分销比
                    .distRatio(Objects.equals(validationContext.getSourceChannel(), SourceChannelType.NATURAL) ? orderConfig.getDistRatio() : null)
                    //B币支付需要配置可结算币（苹果税原因）
                    .settleRatio(isBpPay(platformType) ? orderConfig.getBpSettleRatio() : null)
                    .extra(orderExtra)
                    .build();
            orderService.updateOrder(updateOrder);
            return exchangePayInfo;
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    @Override
    public void confirmPayInfo(OpenPayConfirmInfo req) throws Exception {
        ConfirmValidationContext validationContext = validateConfirm(req);
        Order order = validationContext.getOrder();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), order.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在进行中，请勿重复操作")
                    .build());
            BpQuickPayParam bpQuickPayParam = payWithBpQuick(req, validationContext);
            OrderExtra orderExtra = OrderExtra.builder()
                    /**
                     * 注意：B币支付的支付参数是在confirm阶段确定的，不是在exchange阶段！！！
                     */
                    .payParamInfo(JSONObject.toJSONString(bpQuickPayParam))
                    .build();
            Order updateOrder = Order.builder()
                    .orderId(order.getOrderId())
                    .extra(orderExtra)
                    .build();
            orderService.updateOrder(updateOrder);
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    @Override
    public void notify(PayNotifyInfo notifyInfo) throws Exception {
        NotifyValidationContext validationContext = validateNotify(notifyInfo);
        Order order = validationContext.getOrder();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), order.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在进行中，请勿重复操作")
                    .build());

            PayStatus curPayStatus = PayStatus.getByCodeWithoutEx(order.getPayStatus());
            if (Objects.equals(curPayStatus, PayStatus.SUCCESS)
                    || Objects.equals(curPayStatus, PayStatus.CANCEL)) {
                //如果当前订单已经支付成功/取消了，则可以忽略该回调，比如重试、网络等原因，开平处理成功了，但是支付中台收到失败，然后触发的重试行为
                return;
            }

            //更新订单
            OrderExtra orderExtra = OrderExtra.builder()
                    .payNotifyInfo(JSONObject.toJSONString(notifyInfo))
                    .build();
            Order updateOrder = Order.builder()
                    .orderId(order.getOrderId())
                    .payAmount(notifyInfo.getPayAmount())
                    .payStatus(validationContext.getPayStatus().getCode())
                    .txId(notifyInfo.getTxId())
                    .payChannel(validationContext.getPayChannel().getCode())
                    .payTime(validationContext.getPayTime())
                    .extra(orderExtra)
                    .build();
            orderService.updateOrder(updateOrder);

            //发布订单支付回调事件：事件中只需要核心字段信息即可
            Order payNotifyOrder = Order.builder()
                    .appId(order.getAppId())
                    .orderId(order.getOrderId())
                    .devOrderId(order.getDevOrderId())
                    .traceId(order.getTraceId())
                    .build();
            OpenOrderPayNotifyEvent openOrderPayNotifyEvent = OpenOrderPayNotifyEvent.builder()
                    .actionType(OpenEvent.ActionType.REFRESH)
                    .order(payNotifyOrder)
                    .build();
            openEventPublisher.publishWithoutEx(openOrderPayNotifyEvent);
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    @Override
    public Refund refund(RefundReq req) throws Exception {
        PaymentService paymentService = (PaymentService) AopContext.currentProxy();
        Refund refund = paymentService.doRefund(req);
        OpenRefundEvent refundEvent = OpenRefundEvent.builder()
                .actionType(OpenEvent.ActionType.ADD)
                .refund(refund)
                .build();
        openEventPublisher.publishWithoutEx(refundEvent);
        return refund;
    }

    @Override
    public void updateRefund(Refund orderRefund) throws Exception {
        PaymentService paymentService = (PaymentService) AopContext.currentProxy();
        paymentService.doUpdateRefund(orderRefund);
        OpenRefundEvent refundEvent = OpenRefundEvent.builder()
                .actionType(OpenEvent.ActionType.MODIFY)
                .refund(orderRefund)
                .build();
        openEventPublisher.publishWithoutEx(refundEvent);
    }

    @Override
    public List<Refund> getRefundList(Long orderId) throws Exception {
        return doGetRefund(orderId, null);
    }

    @Override
    public Refund getRefund(Long orderId, String devRefundId) throws Exception {
        AssertUtil.hasText(devRefundId, ErrorCodeType.BAD_DATA.getCode(), "退款批次不能为空");
        List<Refund> refunds = doGetRefund(orderId, devRefundId);
        return CollectionUtils.isEmpty(refunds) ? null : refunds.get(0);
    }

    @Override
    public void refundWithPayPlatform(Long orderId, String devRefundId) throws Exception {
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), orderId);
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在修改或者退款中，请勿重复操作")
                    .build());
            Refund refund = getRefund(orderId, devRefundId);
            if (Objects.isNull(refund)) {
                log.info("[PaymentService] refundWithPayPlatform，give up to refund because of not refund, cur orderId={}, devRefundId={}", orderId, devRefundId);
                return;
            }

            RefundStatus refundStatus = RefundStatus.getByCodeWithoutEx(refund.getRefundStatus());
            if (!Objects.equals(refundStatus, RefundStatus.CREATE_INIT)) {
                log.info("[PaymentService] refundWithPayPlatform，give up to refund because of not supporting refund status, cur refundStatus={}", refundStatus.getName());
                return;
            }

            Order order = orderService.getOrder(refund.getOrderId());
            if (Objects.isNull(order)) {
                log.info("[PaymentService] refundWithPayPlatform，give up to refund because of not order, cur orderId={}", refund.getOrderId());
                return;
            }

            RefundReqAndRes refundReqAndRes = RefundReqAndRes.builder()
                    .req(new RefundParam())
                    .res(new RefundInfo())
                    .build();//default empty in case of EXCEPTION

            RefundStatus refundStatusAfterRefunded;
            try {
                refundReqAndRes = refund2PayPlatform(refund, order);
                refundStatusAfterRefunded = RefundStatus.getByPayPlatformRefundStatusWithoutEx(refundReqAndRes.getRes().getRefundStatus());
                log.info("[PaymentService] refundWithPayPlatform，refund2PayPlatform refundStatusAfterRefunded={}", refundStatusAfterRefunded.getName());
            } catch (Exception e) {
                log.error("[PaymentService] refundWithPayPlatform error, orderId={}, devRefundId={}", orderId, devRefundId, e);
                if (e instanceof ServiceException) {
                    //如果是已知的ServiceException异常，则意味着业务逻辑不可达，直接标记为失败即可
                    refundStatusAfterRefunded = RefundStatus.CREATE_FAILED;
                } else {
                    //非业务逻辑不可达异常，比如网络异常等，则维持原状态，待后续重试
                    //refundStatusAfterRefunded = refundStatus;
                    //抛出异常上游重试
                    //todo: 上报metric
                    throw e;
                }
            }

            //refundStatusAfterRefunded基本上是REFUND_SUCCESS或者REFUND_CREATE
            //如果是首次发起退款，发起这个动作成功的话，应该返回的是REFUND_CREATE
            //如果是再次或者多次发起的退款，发起这个动作成功的话，可能会返回REFUND_SUCCESS，因为之前的退款已经处理成功了
            if (Objects.equals(refundStatus, refundStatusAfterRefunded)) {
                //如果退款状态没有任何变化，则不再更新退款记录以及发送消息，避免消息恶性循环，比如多次退款总是失败
                return;
            }

            Refund updateRefund = Refund.builder()
                    .id(refund.getId())
                    .orderId(refund.getOrderId())
                    .devRefundId(refund.getDevRefundId())
                    .payPlatformRefundId(refundReqAndRes.getRes().getRefundId())
                    .refundStatus(refundStatusAfterRefunded.getCode())
                    .extra(RefundExtra.builder()
                            .refundParamInfo(JSON.toJSONString(refundReqAndRes))
                            .build())
                    .build();
            updateRefund(updateRefund);
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }

    }

    @Override
    public void refundNotify(RefundNotifyInfo notifyInfo) throws Exception {
        RefundNotifyValidationContext validationContext = validateRefundNotify(notifyInfo);
        Order order = validationContext.getOrder();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), order.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在修改或者退款中，请勿重复操作")
                    .build());

            Refund refund = validationContext.getRefund();
            //SUCCESS or FAILED
            RefundStatus refundStatus = validationContext.getRefundStatus();

            Refund updateRefund = Refund.builder()
                    .id(refund.getId())
                    .orderId(order.getOrderId())
                    .devRefundId(refund.getDevRefundId())
                    .refundStatus(refundStatus.getCode())
                    .refundAmount(refund.getRefundAmount())
                    .refundTime(validationContext.getRefundTime())
                    .extra(RefundExtra.builder()
                            .refundNotifyInfo(JSON.toJSONString(notifyInfo))
                            .build())
                    .build();
            updateRefund(updateRefund);
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doUpdateRefund(Refund orderRefund) throws Exception {
        AssertUtil.notNull(orderRefund, ErrorCodeType.BAD_DATA.getCode(), "退款记录不能为空");
        AssertUtil.isTrue(NumberUtil.isPositive(orderRefund.getOrderId()), ErrorCodeType.BAD_DATA.getCode(), "退款记录不能为空");
        AssertUtil.hasText(orderRefund.getDevRefundId(), ErrorCodeType.BAD_DATA.getCode(), "退款记录不能为空");
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), orderRefund.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单正在修改或者退款中，请勿重复操作")
                    .build());
            Refund existOrderRefund = getRefund(orderRefund.getOrderId(), orderRefund.getDevRefundId());
            AssertUtil.notNull(existOrderRefund, ErrorCodeType.BAD_DATA.getCode(), "退款记录不存在");

            MiniAppOpenRefundPo miniAppOpenRefundPo = new MiniAppOpenRefundPo();
            BeanUtils.copyProperties(orderRefund, miniAppOpenRefundPo);
            miniAppOpenRefundPo.setId(existOrderRefund.getId());
            openOrderRefundDao.updateByPrimaryKeySelective(miniAppOpenRefundPo);

            if (Objects.nonNull(orderRefund.getExtra())) {
                MiniAppOpenRefundExtraPo miniAppOpenRefundExtraPo = new MiniAppOpenRefundExtraPo();
                BeanUtils.copyProperties(orderRefund.getExtra(), miniAppOpenRefundExtraPo);
                miniAppOpenRefundExtraPo.setRefundId(null);

                MiniAppOpenRefundExtraPoExample orderRefundExtraPoExample = new MiniAppOpenRefundExtraPoExample();
                orderRefundExtraPoExample.createCriteria()
                        .andRefundIdEqualTo(existOrderRefund.getId())
                        .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                openOrderRefundExtraDao.updateByExampleSelective(miniAppOpenRefundExtraPo, orderRefundExtraPoExample);
            }
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Refund doRefund(RefundReq req) throws Exception {
        log.info("[PaymentService] refundOrder request={}", JSON.toJSONString(req));
        RefundValidationContext validationContext = validateRefund(req);
        Order order = validationContext.getOrder();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_PAY_PROGRESS.getPattern(), order.getOrderId());
        RLock lock = null;
        try {
            lock = cacheRepository.getLock(lockKey, LockOption.builder()
                    .waitTime(10)
                    .leaseTime(60)
                    .unit(TimeUnit.SECONDS)
                    .errMsg("当前订单存在其他编辑或退款行为，请勿重复请求")
                    .build());
            MiniAppOpenRefundPo openRefundPo = MiniAppOpenRefundPo.builder()
                    .orderId(order.getOrderId())
                    .devRefundId(req.getDevRefundId())
                    .refundAmount(req.getRefundAmount())
                    .refundStatus(RefundStatus.CREATE_INIT.getCode())
                    .notifyStatus(NotifyStatus.NO.getCode())
                    .notifyUrl(req.getNotifyUrl())
                    .refundDesc(req.getRefundDesc())
                    .traceId(TraceUtil.genTraceId())
                    .build();
            openOrderRefundDao.insertSelective(openRefundPo);

            MiniAppOpenRefundExtraPo openRefundExtraPo = MiniAppOpenRefundExtraPo.builder()
                    .refundId(openRefundPo.getId())
                    .devExtraData(req.getExtraData())
                    .build();
            openOrderRefundExtraDao.insertSelective(openRefundExtraPo);

            Refund refund = PaymentMapper.MAPPER.toOrderRefund(openRefundPo);
            refund.setExtra(PaymentMapper.MAPPER.toOrderRefundExtra(openRefundExtraPo));
            return refund;
        } finally {
            if (Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }


    private ExchangePayInfo getExchangePayInfoPay(ExchangePayInfoReq req, ExchangeValidationContext validationContext) {
        PlatformType platformType = validationContext.getPlatformType();
        //ios走B币支付
        return isBpPay(platformType) ? getExchangePayInfoPay4Bp(req, validationContext) : getExchangePayInfoPay4Cash(req, validationContext);
    }


    /**
     * B币支付，目前适用于ios
     */
    private ExchangePayInfo getExchangePayInfoPay4Bp(ExchangePayInfoReq req, ExchangeValidationContext validationContext) {
        PaymentConfig paymentConfig = configCenter.getPayment();
        Order order = validationContext.getOrder();
        //step1：查询钱包余额 - UserWalletInfo
        UserWalletInfoParam userWalletInfoParam = UserWalletInfoParam.builder()
                .customerId(paymentConfig.getCustomerId())
                .platformType(1)
                .mid(order.getMid())
                .traceId(order.getTraceId())
                .timestamp(Objects.toString(System.currentTimeMillis()))
                .signType("MD5")
                .build();
        String walletInfoParamSign = PaySignUtil.sign(userWalletInfoParam, paymentConfig.getPayToken());
        userWalletInfoParam.setSign(walletInfoParamSign);
        UserWalletInfo userWalletInfo = call4pay("getUserWalletInfo", UserWalletInfo.class,
                () -> payPlatformAccessLayerRemoteService.getUserWalletInfo(toRequestBody(userWalletInfoParam)));
        BigDecimal availableBp = userWalletInfo.getAvailableBp();
        //将ios的剩余B币转 -> 分
        long balance = Objects.nonNull(availableBp) && availableBp.compareTo(BigDecimal.ZERO) > 0
                ? availableBp.multiply(new BigDecimal("100")).longValue() : 0L;

        ExchangePayInfo.ExchangePayInfoBuilder exchangePayInfoBuilder = ExchangePayInfo.builder()
                .payType(PayType.OPEN_PAY.getCode())//默认开平支付
                .payParam(new JSONObject())//一定不要为null
                .payInfo(OpenPayInfo.builder()
                        .showTitle(paymentConfig.getOpenPayShowTitle())
                        .showContent(String.format("%d B币", (order.getAmount() / 100)))//将分转成B币，1B币=1元
                        .confirmInfo(OpenPayConfirmInfo.builder()
                                .orderId(order.getOrderId())
                                .devOrderId(order.getDevOrderId())
                                .appId(order.getAppId())
                                .openId(order.getOpenId())
                                .mid(order.getMid())
                                .ts(System.currentTimeMillis())
                                .build())
                        .build());

        //step2：选择支付方式 - 快捷支付or充消一体
        if (balance < order.getAmount()) {
            //先充值，再支付
            exchangePayInfoBuilder
                    .payType(PayType.RECHARGE_THEN_OPEN_PAY.getCode())
                    .payParam(getRechargePayParam4Bp(req, validationContext));
        }
        return exchangePayInfoBuilder.build();
    }

    /**
     * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957973">B币快捷支付</a>
     *
     * @param validationContext
     */
    private BpQuickPayParam payWithBpQuick(OpenPayConfirmInfo req, ConfirmValidationContext validationContext) {
        PaymentConfig paymentConfig = configCenter.getPayment();
        Order order = validationContext.getOrder();
        BpQuickPayParam bpQuickPayParam = BpQuickPayParam.builder()
                .customerId(paymentConfig.getCustomerId())
                .orderId(Objects.toString(order.getOrderId()))
                .payBp(Objects.toString(order.getAmount() / 100))//将分转成B币，1B币=1元
                .uid(order.getMid())
                .productId(order.getProductId())
                .productType("1")//虚拟商品
                .showTitle(order.getProductName())
                .unsupportedBpCoupon(1)//不使用B券
                .device("IOS")
                .deviceInfo(StringUtils.hasText(req.getBuvid()) ? req.getBuvid() : "default")//必须存在，否则支付验签失败
                .deviceType(3)
                .traceId(order.getTraceId())
                .extData(validationContext.getPayExtraData().toJSONString())
                .timestamp(System.currentTimeMillis())
                .notifyUrl(paymentConfig.getPayNotifyUrl())//暂定
                .feeType("CNY")
                .orderCreateTime(Objects.toString(order.getCtime().getTime()))
                .orderExpire(paymentConfig.getPayPeriod())
                .signType("MD5")
                .version("1.0")
                .build();
        String bpQuickPayParamSign = PaySignUtil.sign(bpQuickPayParam, paymentConfig.getPayToken());
        bpQuickPayParam.setSign(bpQuickPayParamSign);
        BpQuickPayInfo quickPay = call4pay("quickPay", BpQuickPayInfo.class,
                () -> payPlatformAccessLayerRemoteService.quickPay(toRequestBody(bpQuickPayParam)));
        AssertUtil.isTrue(Objects.nonNull(quickPay) && NumberUtil.isPositive(quickPay.getTxId()),
                ErrorCodeType.SYSTEM_ERROR.getCode(), "确认支付失败");
        return bpQuickPayParam;
    }


    /**
     * B币支付，目前适用于ios
     * 充消一体，返回的参数就是需要充值的参数，充值金额是差值
     */
    private JSONObject getRechargePayParam4Bp(ExchangePayInfoReq req, ExchangeValidationContext validationContext) {
        PaymentConfig paymentConfig = configCenter.getPayment();
        Order order = validationContext.getOrder();
        BpPaymentRechargeConsumeParam rechargeConsumeParam = BpPaymentRechargeConsumeParam.builder()
                .platformType(1)//固定为ios，恰好和开平定义的枚举一致！！！
                .customerId(paymentConfig.getCustomerId())
                .orderId(Objects.toString(order.getOrderId()))
                .payBp(Objects.toString(order.getAmount() / 100))//将分转成B币，1B币=1元
                .uid(order.getMid())
                .productId(order.getProductId())
                .showTitle(order.getProductName())
                .orderCreateTime(order.getCtime().getTime())
                .orderExpire(paymentConfig.getPayPeriod())
                .deviceInfo(req.getBuvid())
                .createUa(req.getUserAgent())
                .traceId(order.getTraceId())
                .extData(validationContext.getPayExtraData().toJSONString())
                .timestamp(System.currentTimeMillis())
                .notifyUrl(paymentConfig.getPayNotifyUrl())//暂定
                .signType("MD5")
                .version("1.0")
                .build();
        String sign = PaySignUtil.sign(rechargeConsumeParam, paymentConfig.getPayToken());
        rechargeConsumeParam.setSign(sign);

        JSONObject data = call4pay("rechargeAndConsume", JSONObject.class,
                () -> payPlatformCashierRemoteService.rechargeAndConsume(toRequestBody(rechargeConsumeParam)));
        //rechargeAndConsume接口返回的payParam信息就是支付SDK需要的完整信息，不需要更改！！！
        return data.getJSONObject("payParam");
    }

    /**
     * 现金支付，目前适用于android
     */
    private ExchangePayInfo getExchangePayInfoPay4Cash(ExchangePayInfoReq req, ExchangeValidationContext validationContext) {
        PaymentConfig paymentConfig = configCenter.getPayment();
        Order order = validationContext.getOrder();
        PayParam payParam = PayParam.builder()
                .customerId(paymentConfig.getCustomerId())
                .orderId(Objects.toString(order.getOrderId()))
                .originalAmount(order.getAmount())
                .payAmount(order.getAmount())
                .uid(order.getMid())
                .productId(order.getProductId())
                .showTitle(order.getProductName())
                .showContent(order.getProductDesc())
                .orderCreateTime(Objects.toString(order.getCtime().getTime()))
                .orderExpire(paymentConfig.getPayPeriod())
                .deviceInfo(req.getBuvid())
                .deviceType(3)//固定3:app
                .createUa(req.getUserAgent())
                .traceId(order.getTraceId())
                .orientation(2)//自适应
                .extData(validationContext.getPayExtraData().toJSONString())
                .timestamp(System.currentTimeMillis())
                .serviceType(paymentConfig.getPayServiceType())
                .notifyUrl(paymentConfig.getPayNotifyUrl())//暂定
                .feeType("CNY")
                .signType("MD5")
                .version("1.0")
                .build();
        String sign = PaySignUtil.sign(payParam, paymentConfig.getPayToken());
        payParam.setSign(sign);
        return ExchangePayInfo.builder()
                .payType(PayType.SDK_PAY.getCode())
                .payInfo(new OpenPayInfo())
                .payParam((JSONObject) JSONObject.toJSON(payParam))
                .build();
    }

    //public static void main(String[] args) {
    //    String s = "{\"deviceType\":3,\"serviceType\":0,\"traceId\":\"8948d5699620482bbdee272321669332\",\"orientation\":2,\"productId\":\"1\",\"orderId\":\"1000366\",\"orderCreateTime\":\"1739956426000\",\"showContent\":\"\",\"sign\":\"29417ede4f5ac10ec2be78bc0fb97fca\",\"createUa\":\"Mozilla/5.0 BiliDroid/8.33.0 (<EMAIL>) BiliSmallApp/4.0.5 os/android model/MAA-AN10 mobi_app/android build/8330200 channel/yingyongbao innerVer/8330210 osVer/15 network/2\",\"orderExpire\":900,\"feeType\":\"CNY\",\"version\":\"1.0\",\"uid\":1727160698,\"payAmount\":11,\"originalAmount\":11,\"extData\":\"{\\\"openId\\\":\\\"1ccd51ee98e7d6d3e33fcd4a7f4e04ab\\\",\\\"appId\\\":\\\"bili30737a16a5ec7ed0\\\"}\",\"showTitle\":\"测试商品\",\"customerId\":10082,\"notifyUrl\":\"https://miniapp.bilibili.co/open/web_api/v1/platform/payment/notify\",\"signType\":\"MD5\",\"timestamp\":1739956426917}";
    //    PayParam javaObject = JSON.toJavaObject(JSON.parseObject(s), PayParam.class);
    //    String key = "58ad6387c0867c766c9993dbc948961d";
    //    String sign1 = PaySignUtil.sign(javaObject, key);
    //    System.out.println("正确sign：2083265fdd738cc148f16d15ae9b6262");
    //    System.out.println("sign：" + sign1);
    //}


    /**
     * 向支付中台发起退款
     */
    private RefundReqAndRes refund2PayPlatform(Refund refund, Order order) {
        PaymentConfig paymentConfig = configCenter.getPayment();
        RefundParam refundParam = RefundParam.builder()
                .customerId(paymentConfig.getCustomerId())
                .txId(Long.valueOf(order.getTxId()))
                .orderId(Objects.toString(order.getOrderId()))
                .customerRefundId(refund.getDevRefundId())
                .totalAmount(order.getPayAmount())
                .refundAmount(refund.getRefundAmount())
                //不可超过32字符，否则报{"errno":8004010003,"msg":"MISSING_PARAMS"}
                .refundDesc(StringUtils.hasText(refund.getRefundDesc()) && refund.getRefundDesc().length() > 32
                        ? refund.getRefundDesc().substring(0, 32) : refund.getRefundDesc())
                .extData("{}")
                .notifyUrl(paymentConfig.getRefundNotifyUrl())
                .refundType(0)//原路退款给用户
                .traceId(refund.getTraceId())
                .timestamp(System.currentTimeMillis())
                .signType("MD5")
                .version("1.0")
                .build();
        String sign = PaySignUtil.sign(refundParam, paymentConfig.getPayToken());
        refundParam.setSign(sign);
        RefundInfo refundInfo = call4pay("refund", RefundInfo.class, () -> payPlatformRemoteService.refund(toRequestBody(refundParam)));
        return RefundReqAndRes.builder()
                .req(refundParam)
                .res(Objects.isNull(refundInfo) ? new RefundInfo() : refundInfo)
                .build();
    }


    private <T> T call4pay(String methodName, Class<T> returnType, Supplier<BiliCall<BiliPayResponse<JSONObject>>> caller) {
        BiliPayResponse<JSONObject> res;
        try {
            res = caller.get().execute().body();
            log.info("[PaymentService] call4pay, methodName={}, res={}", methodName, JSON.toJSONString(res));
        } catch (BiliBreakerRejectedException e) {
            log.error("[PaymentService] call4pay {} BiliBreakerRejectedException error", methodName, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_BUSY.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("[PaymentService] call4pay {} error", methodName, e);
            throw e;
        }

        if (Objects.isNull(res)) {
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "暂无数据");
        }

        JSONObject data = res.getData();

        if (Objects.equals(PayErrorCodeType.SUCCESS.getCode(), res.getErrno())) {
            return data.toJavaObject(returnType);
        }

        //风控
        if (Objects.equals(PayErrorCodeType.RISK_PAYMENT.getCode(), res.getErrno())) {
            JSONObject riskInfo = data.getJSONObject("code");
            log.warn("[PaymentService] {} risk error，errno={}, msg={}, riskInfo={}", methodName, res.getErrno(),
                    res.getMsg(), riskInfo);
            String riskMsg = Objects.nonNull(riskInfo) ? riskInfo.getString("shieldWarnTips") : "";
            if (!StringUtils.hasText(riskMsg)) {
                riskMsg = "当前交易存在风险，无法继续支付。如有疑问请联系客服咨询！";
            }
            throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), riskMsg);
        }

        if (PayErrorCodeType.isGracefully(res.getErrno())) {
            //如果可以对外披露，则返回提示
            log.warn("[PaymentService] {} error，errno={}, msg={}", methodName, res.getErrno(), res.getMsg());
            throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), res.getMsg());
        }

        log.warn("[PaymentService] {} error，errno={}, msg={}", methodName, res.getErrno(), res.getMsg());
        throw new ServiceException(ErrorCodeType.BAD_REQUEST);
    }

    /**
     * 是否是B币支付
     */
    private boolean isBpPay(PlatformType platformType) {
        //目前只有端的条件，后期可能有其他条件
        return platformType.isSupportsBpPay();
    }


    public List<Refund> doGetRefund(Long orderId, String devRefundId) throws Exception {
        AssertUtil.isTrue(NumberUtil.isPositive(orderId), ErrorCodeType.BAD_DATA.getCode(), "订单id不能为空");

        MiniAppOpenRefundPoExample refundPoExample = new MiniAppOpenRefundPoExample();
        MiniAppOpenRefundPoExample.Criteria criteria = refundPoExample.createCriteria().andOrderIdEqualTo(orderId);
        FunctionUtil.hasText(devRefundId, criteria::andDevRefundIdEqualTo);

        List<MiniAppOpenRefundPo> miniAppOpenRefundPos = openOrderRefundDao.selectByExample(refundPoExample);
        if (CollectionUtils.isEmpty(miniAppOpenRefundPos)) {
            return Lists.newLinkedList();
        }
        List<Long> refundIdList = miniAppOpenRefundPos.stream()
                .map(MiniAppOpenRefundPo::getId)
                .collect(Collectors.toList());
        Map<Long, RefundExtra> refundExtraMap = getRefundExtra(refundIdList)
                .stream()
                .collect(Collectors.toMap(RefundExtra::getRefundId, Function.identity()));

        List<Refund> refunds = PaymentMapper.MAPPER.toOrderRefund(miniAppOpenRefundPos);
        refunds.forEach(refund -> refund.setExtra(refundExtraMap.get(refund.getId())));
        return refunds;
    }

    /**
     *
     */
    public List<RefundExtra> getRefundExtra(List<Long> refundIdList) throws Exception {
        if (CollectionUtils.isEmpty(refundIdList)) {
            return Collections.emptyList();
        }

        MiniAppOpenRefundExtraPoExample refundExtraPoExample = new MiniAppOpenRefundExtraPoExample();
        refundExtraPoExample.createCriteria().andRefundIdIn(refundIdList);

        List<MiniAppOpenRefundExtraPo> miniAppOpenRefundPos = openOrderRefundExtraDao.selectByExample(refundExtraPoExample);
        return PaymentMapper.MAPPER.toOrderRefundExtra(miniAppOpenRefundPos);
    }

    private ExchangeValidationContext validateExchange(ExchangePayInfoReq req) throws Exception {
        AssertUtil.notNull(req, ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.notNull(req.getOrder(), ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");

        OrderCreateRes orderCreateRes = req.getOrder();
        Order order = orderService.getOrder(orderCreateRes.getOrderId());

        //严格校验订单，必须保证解密后的数据完全一致
        AssertUtil.notNull(order, ErrorCodeType.BAD_DATA.getCode(), "订单不存在");
        AssertUtil.isTrue(Objects.equals(order.getDevOrderId(), orderCreateRes.getDevOrderId()), ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.isTrue(Objects.equals(order.getAppId(), orderCreateRes.getAppId()), ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.isTrue(Objects.equals(order.getOpenId(), orderCreateRes.getOpenId()), ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.isTrue(Objects.equals(order.getMid(), orderCreateRes.getMid()), ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.isTrue(!Objects.equals(order.getPayStatus(), PayStatus.SUCCESS.getCode()), ErrorCodeType.BAD_DATA.getCode(), "该订单已支付完成，请勿重复支付");

        PlatformType platformType = PlatformType.getByName(req.getPlatform());
        if (isBpPay(platformType)) {
            //B币支付，金额必须是整元，不能包含角、分等
            AssertUtil.isTrue((order.getAmount() % 100) == 0, ErrorCodeType.BAD_DATA.getCode(),
                    "当前交易金额不支持（B币支付仅支持整元），此次无法继续支付。如有疑问请联系客服咨询！");
        }

        JSONObject traceInfo = Objects.isNull(req.getTraceInfo()) ? new JSONObject() : req.getTraceInfo();
        //如果存在track_id则认为是商业流量
        SourceChannelType sourceChannel = StringUtils.hasText(traceInfo.getString(TracePropertyKey.TRACK_ID))
                ? SourceChannelType.AD : SourceChannelType.NATURAL;

        //透传给支付中台，方便排查问题
        JSONObject extraData = new JSONObject();
        extraData.put("openId", order.getOpenId());
        extraData.put("appId", order.getAppId());

        return ExchangeValidationContext.builder()
                .platformType(platformType)
                .sourceChannel(sourceChannel)
                .order(order)
                .traceInfo(traceInfo)
                .payExtraData(extraData)
                .build();
    }

    private RefundNotifyValidationContext validateRefundNotify(RefundNotifyInfo notifyInfo) throws Exception {
        AssertUtil.notNull(notifyInfo, ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.hasText(notifyInfo.getOrderId(), ErrorCodeType.BAD_DATA.getCode(), "业务方订单号不能为空");
        AssertUtil.isTrue(NumberUtil.isPositive(notifyInfo.getTxId()), ErrorCodeType.BAD_DATA.getCode(), "支付平台支付id不能为空");

        PaymentConfig paymentConfig = configCenter.getPayment();
        AssertUtil.isTrue(Objects.equals(paymentConfig.getCustomerId(), notifyInfo.getCustomerId()), ErrorCodeType.BAD_DATA.getCode(), "业务方id和支付时不一致");

        Order order = orderService.getOrder(Long.parseLong(notifyInfo.getOrderId()));
        AssertUtil.notNull(order, ErrorCodeType.BAD_DATA.getCode(), "订单不存在");

        List<RefundBatch> batchRefundList = notifyInfo.getBatchRefundList();
        RefundBatch curRefundBatch = CollectionUtils.isEmpty(batchRefundList) ? null : batchRefundList.get(0);
        AssertUtil.notNull(curRefundBatch, ErrorCodeType.BAD_DATA.getCode(), "退款批次不存在");

        RefundStatus refundStatus = RefundStatus.getByPayPlatformRefundStatusWithoutEx(curRefundBatch.getRefundStatus());
        AssertUtil.isTrue(Objects.equals(refundStatus, RefundStatus.SUCCESS)
                || Objects.equals(refundStatus, RefundStatus.FAILED), ErrorCodeType.BAD_DATA.getCode(), "不支持的退款状态：" + curRefundBatch.getRefundStatus());

        Refund refund = getRefund(Long.parseLong(notifyInfo.getOrderId()), curRefundBatch.getCustomerRefundId());
        AssertUtil.notNull(refund, ErrorCodeType.BAD_DATA.getCode(), "当前退款批次在业务库中不存在");
        AssertUtil.isTrue(Objects.equals(curRefundBatch.getRefundAmount(), refund.getRefundAmount()), ErrorCodeType.BAD_DATA.getCode(), "退款金额和实际退款金额不一致");

        Timestamp refundTime = new Timestamp(System.currentTimeMillis());//默认当前回调时间
        if (StringUtils.hasText(curRefundBatch.getRefundEndTime())) {
            refundTime = TimeUtil.getTimestampByStr(curRefundBatch.getRefundEndTime(), TimeUtil.DATE_TIME_FORMAT);
        }

        return RefundNotifyValidationContext.builder()
                .order(order)
                .refund(refund)
                .refundBatch(curRefundBatch)
                .refundTime(refundTime)
                .refundStatus(refundStatus)
                .build();
    }


    private NotifyValidationContext validateNotify(PayNotifyInfo notifyInfo) throws Exception {
        AssertUtil.notNull(notifyInfo, ErrorCodeType.BAD_DATA.getCode(), "非法数据请求");
        AssertUtil.hasText(notifyInfo.getOrderId(), ErrorCodeType.BAD_DATA.getCode(), "业务方订单号不能为空");
        AssertUtil.hasText(notifyInfo.getTxId(), ErrorCodeType.BAD_DATA.getCode(), "支付平台支付id不能为空");

        Order order = orderService.getOrder(Long.parseLong(notifyInfo.getOrderId()));
        AssertUtil.notNull(order, ErrorCodeType.BAD_DATA.getCode(), "订单不存在");

        PaymentConfig paymentConfig = configCenter.getPayment();
        AssertUtil.isTrue(Objects.equals(paymentConfig.getCustomerId(), notifyInfo.getCustomerId()), ErrorCodeType.BAD_DATA.getCode(), "业务方id和支付时不一致");
        AssertUtil.isTrue(Objects.equals(order.getAmount(), notifyInfo.getPayAmount()), ErrorCodeType.BAD_DATA.getCode(), "支付金额和支付时不一致");

        PayChannelType payChannel = PayChannelType.getByChannelWithoutEx(notifyInfo.getPayChannel());
        PayStatus payStatus = PayStatus.getByNameWithoutEx(notifyInfo.getPayStatus());
        Timestamp payTime = new Timestamp(System.currentTimeMillis());//默认当前回调时间
        if (StringUtils.hasText(notifyInfo.getOrderPayTime())) {
            payTime = TimeUtil.getTimestampByStr(notifyInfo.getOrderPayTime(), TimeUtil.DATE_TIME_FORMAT);
        }
        return NotifyValidationContext.builder()
                .order(order)
                .payChannel(payChannel)
                .payStatus(payStatus)
                .payTime(payTime)
                .build();
    }


    private ConfirmValidationContext validateConfirm(OpenPayConfirmInfo confirmInfo) throws Exception {
        AssertUtil.notNull(confirmInfo, ErrorCodeType.BAD_DATA.getCode(), "订单信息不可为空");
        Order order = orderService.getOrder(confirmInfo.getOrderId());
        AssertUtil.notNull(order, ErrorCodeType.NO_DATA.getCode(), "订单不存在");
        AssertUtil.isTrue(Objects.equals(confirmInfo.getDevOrderId(), order.getDevOrderId()), ErrorCodeType.BAD_DATA.getCode(), "非法数据");
        AssertUtil.isTrue(Objects.equals(confirmInfo.getAppId(), order.getAppId()), ErrorCodeType.BAD_DATA.getCode(), "非法数据");
        AssertUtil.isTrue(Objects.equals(confirmInfo.getOpenId(), order.getOpenId()), ErrorCodeType.BAD_DATA.getCode(), "非法数据");
        AssertUtil.isTrue(PayStatus.isPermitPay(order.getPayStatus()), ErrorCodeType.BAD_DATA.getCode(), "订单已支付/已确认，请勿重复支付");

        PaymentConfig paymentConfig = configCenter.getPayment();
        Integer payPeriod = paymentConfig.getPayPeriod();
        Duration duration = Duration.between(order.getCtime().toLocalDateTime(), LocalDateTime.now());
        AssertUtil.isTrue(!duration.isNegative() && duration.getSeconds() <= payPeriod, ErrorCodeType.BAD_DATA.getCode(), "支付已过期，请重新下单");

        //透传给支付中台，方便排查问题
        JSONObject extraData = new JSONObject();
        extraData.put("openId", order.getOpenId());
        extraData.put("appId", order.getAppId());

        return ConfirmValidationContext.builder()
                .order(order)
                .payExtraData(extraData)
                .build();
    }

    private RefundValidationContext validateRefund(RefundReq req) throws Exception {
        AssertUtil.notNull(req, ErrorCodeType.BAD_DATA.getCode(), "退款请求不能为空");
        AssertUtil.hasText(req.getDevOrderId(), ErrorCodeType.BAD_DATA.getCode(), "dev_order_id不能为空");
        AssertUtil.hasText(req.getAppId(), ErrorCodeType.BAD_DATA.getCode(), "app_id不能为空");
        AssertUtil.hasText(req.getDevRefundId(), ErrorCodeType.BAD_DATA.getCode(), "dev_refund_id不能为空");
        AssertUtil.isTrue(NumberUtil.isPositive(req.getOrderId()), ErrorCodeType.BAD_DATA.getCode(), "order_id不能为空");
        AssertUtil.isTrue(NumberUtil.isPositive(req.getRefundAmount()), ErrorCodeType.BAD_DATA.getCode(), "refund_amount必须大于0");
        //特殊符号检查
        if (StringUtils.hasText(req.getNotifyUrl())) {
            AssertUtil.notContainsAny(req.getNotifyUrl(), new char[]{'?'}, ErrorCodeType.BAD_DATA.getCode(), "notify_url不能携带任何参数，请检查是否包含'?'");
        }
        OpenAccessBo openAccess = openAccessService.getAccess(req.getAccessKey());
        AssertUtil.notNull(openAccess, ErrorCodeType.BAD_DATA.getCode(), "非法请求");

        Order order = orderService.getOrder(req.getOrderId());
        AssertUtil.notNull(order, ErrorCodeType.NO_DATA.getCode(), "订单不存在");
        PayStatus payStatus = PayStatus.getByCodeWithoutEx(order.getPayStatus());
        SettleStatus settleStatus = SettleStatus.getByCodeWithoutEx(order.getSettleStatus());
        AssertUtil.isTrue(Objects.equals(order.getAccessKey(), req.getAccessKey()), ErrorCodeType.BAD_DATA.getCode(), "非法请求");
        AssertUtil.isTrue(Objects.equals(order.getAppId(), req.getAppId()), ErrorCodeType.BAD_DATA.getCode(), "非法请求");
        AssertUtil.isTrue(Objects.equals(order.getDevOrderId(), req.getDevOrderId()), ErrorCodeType.BAD_DATA.getCode(), "非法请求");
        AssertUtil.isTrue(Objects.equals(payStatus, PayStatus.SUCCESS), ErrorCodeType.BAD_DATA.getCode(), "未支付成功的订单不支持退款");
        AssertUtil.isTrue(Objects.equals(settleStatus, SettleStatus.NO), ErrorCodeType.BAD_DATA.getCode(), "已结算的订单不支持退款");
        AssertUtil.isTrue(req.getRefundAmount() <= order.getPayAmount(), ErrorCodeType.BAD_DATA.getCode(), "退款金额不能大于支付金额");

        List<Refund> orderRefundList = getRefundList(order.getOrderId());
        boolean existRefund = orderRefundList.stream().anyMatch(refund -> Objects.equals(refund.getDevRefundId(), req.getDevRefundId()));
        AssertUtil.isTrue(!existRefund, ErrorCodeType.BAD_DATA.getCode(), "当前退款已经存在，请勿重复退款");

        //累计退款金额
        Long accumulateRefundAmount = orderRefundList.stream()
                .map(Refund::getRefundAmount)
                .reduce(Long::sum)
                .orElse(0L);
        AssertUtil.isTrue(accumulateRefundAmount + req.getRefundAmount() <= order.getPayAmount(),
                ErrorCodeType.BAD_DATA.getCode(), "总退款金额不能大于支付金额");

        return RefundValidationContext.builder()
                .order(order)
                .openAccess(openAccess)
                .build();
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ExchangeValidationContext {
        private Order order;
        private SourceChannelType sourceChannel;
        private PlatformType platformType;
        private JSONObject traceInfo;
        private JSONObject payExtraData;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class NotifyValidationContext {
        private Order order;
        private PayChannelType payChannel;
        private PayStatus payStatus;
        private Timestamp payTime;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class RefundValidationContext {
        private Refund existOrderRefund;
        private OpenAccessBo openAccess;
        private Order order;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ConfirmValidationContext {
        private Order order;
        private JSONObject payExtraData;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class RefundNotifyValidationContext {
        private Order order;
        private Refund refund;
        private RefundBatch refundBatch;
        private Timestamp refundTime;
        private RefundStatus refundStatus;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class RefundReqAndRes {
        private RefundParam req;
        private RefundInfo res;
    }

}
