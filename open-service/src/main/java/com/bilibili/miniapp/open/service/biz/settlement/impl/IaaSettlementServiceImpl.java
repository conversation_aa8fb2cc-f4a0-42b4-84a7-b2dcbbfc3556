package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.common.util.IteratorHelper;
import com.bilibili.miniapp.open.common.util.SnowFlakeIdGenerator;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaAppAccountRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaCrmChargeBillRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaSettlementRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaWithdrawBillRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo;
import com.bilibili.miniapp.open.service.biz.settlement.CrmChargeService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettleRuleService;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementService;
import com.bilibili.miniapp.open.service.biz.settlement.event.WithdrawBillStatusUpdateEvent;
import com.bilibili.miniapp.open.service.biz.settlement.exception.SettlementRetryableException;
import com.bilibili.miniapp.open.service.biz.settlement.exception.WithdrawRetryableException;
import com.bilibili.miniapp.open.service.biz.settlement.model.*;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaCrmChargeBill.CrmChargeBillExtra;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlement.SettleExtra;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill.AccrualExtra;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill.BillExtra;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill.ExpenseExtra;
import com.bilibili.miniapp.open.service.biz.settlement.model.SettleRule.DailyIncreasingBenefit;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BindingBusinessAccount;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BusinessEntityInfo;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BusinessEntityInfoProvider;
import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams.SimpleOcrResult;
import com.bilibili.miniapp.open.service.biz.settlement.vo.WithdrawApplyRequest;
import com.bilibili.miniapp.open.service.biz.settlement.vo.WithdrawApplyResult;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig;
import com.bilibili.miniapp.open.service.mapper.SettlementMapper;
import com.bilibili.miniapp.open.service.rpc.http.model.*;
import com.bilibili.miniapp.open.service.util.DateTimeUtil;
import com.bilibili.miniapp.open.service.util.ForkJoinUtil;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.vavr.Lazy;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IaaSettlementServiceImpl implements IaaSettlementService {


    private final IaaAppAccountRepository iaaAppAccountRepository;

    private final IaaSettlementRepository iaaSettlementRepository;

    private final BusinessEntityInfoProvider miniGameBusinessInfoProvider;

    private final IaaWithdrawBillRepository iaaWithdrawBillRepository;

    private final CrmChargeService crmChargeService;

    private final IaaCrmChargeBillRepository iaaCrmChargeBillRepository;

    private final SettleRule settleRule;

    private final IaaSettlementConfig iaaSettlementConfig;

    private final HuilianyiPaymentService huilianyiPaymentService;

    private final ApplicationEventPublisher applicationEventPublisher;
    private final SnowFlakeIdGenerator snowFlakeIdGenerator = new SnowFlakeIdGenerator();

    private final ThreadPoolExecutor iaaSettlementThreadPoolExecutor;

    private final ThreadPoolExecutor iaaDailyScheduleExecutor;

    private final IaaSettleRuleService iaaSettleRuleService;

    /***
     com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#processHistoricalSettlements("20250328")
     * @param logdateLte
     */
    @Override
    public void processHistoricalSettlements(String logdateLte) {

        IaaSettlementServiceImpl proxy = ((IaaSettlementServiceImpl) AopContext.currentProxy());

        iaaDailyScheduleExecutor.submit(() -> {
            String fLogdateLte = Optional
                    .ofNullable(Try.of(() -> DateTimeUtil.fromLogDate(logdateLte)).getOrNull())
                    .map(DateTimeUtil::toLogdate)
                    .orElse(DateTimeUtil.toLogdate(LocalDateTime.now().minusDays(1).toLocalDate()));


            Iterator<List<IaaSettlement>> iterator = IteratorHelper.buildIterator(
                    (id, limit) -> {

                        return iaaSettlementRepository.selectAllByIdGtAndLimitAndStatusInAndLogdateLteEq(
                                Lists.newArrayList(IaaSettlementStatus.init.name()), id, limit, fLogdateLte, null
                        ).stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());
                    },

                    IaaSettlement::getId,

                    iaaSettlementConfig.getScanHistorySettlementsBatchSize()

            );

            while (iterator.hasNext()) {

                List<IaaSettlement> initialSettlements = iterator.next();

                initialSettlements.forEach(settlement -> {
                    try {
                        // 区别点，历史账单归在流水发生时刻
                        proxy.onDailySettlement(
                                SettlementModelConvertor.convertor.dailySettlement2DailyEvent(settlement)
                                        .setGenerateWithdrawDateByLogdate(true)
                        );
                    } catch (Throwable t) {
                        log.error("Fail to process historical settlement, data={}", settlement, t);
                    }
                });
            }

        });


    }

    /**
     * com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#onDailySettlementsReady("20241222")
     *
     * @param logdate
     */
    @Override
    public void onDailySettlementsReady(String logdate) {
        IaaSettlementServiceImpl proxy = ((IaaSettlementServiceImpl) AopContext.currentProxy());


        iaaDailyScheduleExecutor.submit(() -> {

            // 调整为每天触发所有的历史init的计算，避免事件遗漏
            String fLogdateLte = Optional
                    .ofNullable(Try.of(() -> DateTimeUtil.fromLogDate(logdate)).getOrNull())
                    .map(DateTimeUtil::toLogdate)
                    .orElse(DateTimeUtil.toLogdate(LocalDateTime.now().minusDays(1).toLocalDate()));

            Iterator<List<IaaSettlement>> iterator = IteratorHelper.buildIterator(
                    (id, limit) -> {
                        // 此处由于是正序扫描，可能由于历史上积压的init任务而导致耗时越来越长
                        return iaaSettlementRepository.selectAllByIdGtAndLimitAndStatusInAndLogdateLteEq(
                                Lists.newArrayList(IaaSettlementStatus.init.name()), id, limit, fLogdateLte, null
                        ).stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());
                    },

                    IaaSettlement::getId,

                    iaaSettlementConfig.getScanHistorySettlementsBatchSize()
            );

            long startTs = System.currentTimeMillis();
            AtomicInteger roundCnt = new AtomicInteger();
            while (iterator.hasNext()) {

                long roundTs = System.currentTimeMillis();

                List<IaaSettlement> initialSettlements = iterator.next();

//                Map<Long, IaaWithdrawBill> bills = iaaWithdrawBillRepository.selectAllByIdIn(
//                        initialSettlements.stream().map(iaaSettlement -> iaaSettlement.getWithdrawBillId())
//                                .distinct().filter(Objects::nonNull).collect(Collectors.toList())
//                ).stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toMap(
//                        IaaWithdrawBill::getId, bill -> bill
//                ));


                ListUtils.partition(initialSettlements, 8).forEach(partition -> {
                    try {
                        ForkJoinUtil.runnableForkJoin(partition.stream().map(settlement -> (Runnable) () -> {
                            try {
                                IaaDailyIncomeEvent daily = SettlementModelConvertor.convertor.dailySettlement2DailyEvent(
                                        settlement);
//                                // 尽量使用原有的账期
//                                Optional.ofNullable(bills.get(settlement.getWithdrawBillId())).ifPresent(bill -> {
//                                    if (bill.getWithdrawDate() != null) {
//                                        daily.setWithdrawDate(bill.getWithdrawDate());
//                                    }
//                                });
                                // 日常结算，账单归在结算发生时间
                                proxy.onDailySettlement(daily);

                            } catch (Throwable t) {
                                log.error("Fail to process settlement, data={}", settlement, t);
                            }
                        }).collect(Collectors.toList()), iaaSettlementThreadPoolExecutor);
                    } catch (Throwable ex) {
                        log.error("Fail to handle partition={} ", partition, ex);
                    }
                });


                log.info("Finish round daily settlement cost={}ms, round={}",
                        System.currentTimeMillis() - roundTs, roundCnt.incrementAndGet());
            }

            log.info("Complete onDailySettlementsReady, cost={}ms", System.currentTimeMillis() - startTs);
        });


    }

    /**
     * com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#onDailySettlement({"trafficType": "natural", "appId": "123", "appType": "mini_game", "incomeAmt": 100.00, "logdate": "20220317", "withdrawDate":"202503_2"})
     * @param dailySum
     */
    @Override
    public void onDailySettlement(IaaDailyIncomeEvent dailySum) {

        dailySum.validate();

        SettleContext context = this.prepareSettleContext(dailySum);

        DailyIncreasingBenefit dailyBenefit = ((IaaSettlementServiceImpl) AopContext.currentProxy()).doSettleTransaction(
                context);

        // 目前的需求看，CRM充值事务与结算事务分离
        if (dailyBenefit.getCrmCharge().compareTo(BigDecimal.ZERO) > 0 && context.getChargeBill().isPresent()) {
            this.chargeCrmBill(
                    dailyBenefit.getCrmCharge(),
                    context.getChargeBill().get().getLogdate(),
                    context.getBindingBusinessAccount()
                            .map(BindingBusinessAccount::getAccountId),
                    context.getChargeBill().get()
            );
        }

    }


    /**
     * 准备结算上下文
     */
    private SettleContext prepareSettleContext(IaaDailyIncomeEvent dailySum) {

//        SettleRule ruleSnapshot = settleRule.snapshot();
        // 使用DB的分成规则
        IaaSettleRuleBo iaaSettleRule = iaaSettleRuleService.getIaaSettleRule(dailySum.getLogdate());
        if (iaaSettleRule == null) {
            // 没有分成规则不能结算
            log.error("[IaaSettlementServiceImpl] prepareSettleContext Fail to get settle rule for logdate={}, ", dailySum.getLogdate());
            throw new IllegalArgumentException("未找到结算规则");
        }
        SettleRule ruleSnapshot = SettlementMapper.MAPPER.toSettleRule(iaaSettleRule);

        IaaAppAccount account = SettlementModelConvertor.convertor.fromPo(
                iaaAppAccountRepository.selectOrInsertByAppTypeAndAppId(dailySum.getAppType().name(),
                        dailySum.getAppId())
        );

        IaaWithdrawBill bill = tryGetWithdrawBillOfTargetBillDateOrElseNearestBillDate(

                // FIXME: 账期的选择，非常重要，目前产品的口径： 合同如果到期， 当前账期内已经结算的金额能够正常通过提现单提现。 未结算的部分按照每半月的体现单顺延至下一个提现单中。 例如1.13 合同到期， 1-13 的 归属上半月账单， 1.14~1.15 金额归属下半月提现单。
                dailySum.getCandidateWithdrawDateInOrder(),

                withdrawDate -> {
                    IaaWithdrawBill b = SettlementModelConvertor.convertor.fromPo(
                            iaaWithdrawBillRepository.insertOrGetWithdrawBillByAppAndWithdrawDate(
                                    dailySum.getAppType().name(),
                                    dailySum.getAppId(),
                                    // TODO 时间的选择 是按照账单日期还是按照现在的结算日期. 目前选择结算日期
                                    withdrawDate.getWithdrawDate(),
                                    DateTimeUtil.localDateTime2Date(withdrawDate.getBeginTime()),
                                    DateTimeUtil.localDateTime2Date(withdrawDate.getEndTime())
                            ));

                    if (b.getBillStatus() != IaaWithdrawBillStatus.init) {
                        // TODO
                        log.warn("Withdraw bill already settled, skip, bill={}", b);
                        throw new IllegalArgumentException("提现账单已经结算");
                    }

                    return b;
                }
        );

        IaaSettlement settlement = SettlementModelConvertor.convertor
                .fromPo(iaaSettlementRepository.getOrElseInsertSettlement(
                        dailySum.getAppType().name(),
                        dailySum.getAppId(),
                        dailySum.getLogdate(),
                        dailySum.getTrafficType().name(),
                        dailySum.getIncomeAmt(),
                        bill.getId(),
                        JsonUtil.writeValueAsString(
                                new SettleExtra().setRule(ruleSnapshot)
                        )
                ));

        if (settlement.getSettleStatus().getCode() >= IaaSettlementStatus.settled.getCode()) {
            log.warn("Settlement already settled, skip, settlement={}", settlement);
            throw new IllegalArgumentException("结算已完成");
        }

        if (!Objects.equals(settlement.getWithdrawBillId(), bill.getId())) {
            log.warn("Settlement and withdraw bill not match, try rebind,settlement={}, bill={}, ", settlement, bill);
            // 如果发生了get而不是insert，同时这条记录是很久前被生成的，那么会发生不匹配.
            iaaSettlementRepository.updateByPrimaryKeySelective(SettlementModelConvertor.convertor.toPo(
                    new IaaSettlement()
                            .setId(settlement.getId())
                            .setWithdrawBillId(bill.getId())
                            .setSettleKey(Joiner.on("_").join(
                                    settlement.getAppId(), settlement.getLogdate(), settlement.getTrafficType(),
                                    settlement.getAppType()))
                            .setExtra(JsonUtil.writeValueAsString(
                                    new SettleExtra().setRule(ruleSnapshot)
                            ))
            ));
        }

        Optional<BusinessEntityInfo> businessEntity = miniGameBusinessInfoProvider.queryBusinessEntityInfoByAppId(
                dailySum.getAppId());

        Optional<IaaCrmChargeBill> chargeBill = dailySum.getTrafficType() == IaaTrafficType.business ?
                Optional.of(SettlementModelConvertor.convertor.fromPo(
                        iaaCrmChargeBillRepository.insertOrGetCrmChargeBill(
                                dailySum.getAppType().name(),
                                dailySum.getAppId(),
                                dailySum.getLogdate(),
                                dailySum.getTrafficType().name(),
                                dailySum.getIncomeAmt(),
                                bill.getId(),
                                settlement.getId(),
                                JsonUtil.writeValueAsString(new CrmChargeBillExtra().setRule(ruleSnapshot))
                        ))) : Optional.empty();


        Optional<BindingBusinessAccount> bindingAccount = miniGameBusinessInfoProvider.queryBindingBusinessAccountByAppId(
                dailySum.getAppId());

        return new SettleContext()
                .setDailySum(dailySum)
                .setAppAccount(account)
                .setSettlement(settlement)
                .setNearestSettlingWithdrawBill(bill)
                .setBusinessEntity(businessEntity)
                .setChargeBill(chargeBill)
                .setSettleRule(ruleSnapshot)
                .setBindingBusinessAccount(bindingAccount);

    }


    // TODO
    // 账单日的选择直接选择处理时间的账单日，而非logdate的账单日，这里为了避免重放时提现单已经提现或者对存量账单处理的异常。或者是转体现处理的异常
    // 1. 指定的账单日
    // 2. 当前处理时间对应结算日，比如当前为（1-16）那么选择1-15的账单日
    // 3. 当前处理时间继续往下递推，直到得到可以选择的账单日。
    // 结算原则是不按照收入发生时间，而是按照结算的处理时间。这是为了避免重放时提现单已经提现.
    private IaaWithdrawBill tryGetWithdrawBillOfTargetBillDateOrElseNearestBillDate(
            List<WithdrawDate> candidatesDate,
            Function<WithdrawDate, IaaWithdrawBill> dateToBillFunc) {


        if (CollectionUtils.isEmpty(candidatesDate)) {
            throw new IllegalArgumentException("找不到可用的账单日");
        }

        Try<IaaWithdrawBill> tryBill = null;
        for (WithdrawDate withdrawDate : candidatesDate) {

            tryBill = Try.of(() -> dateToBillFunc.apply(withdrawDate))
                    .onFailure(t -> log.warn(
                            "Fail to get withdraw bill by target date, try get nearest date, targetDate={},", withdrawDate, t));

            if (tryBill.isSuccess()) {
                return tryBill.get();
            }

        }

        return tryBill.get();

    }


    /**
     * 执行结算事务
     */
    @Transactional(value = "miniAppTransactionManager", noRollbackFor = SettlementRetryableException.class)
    public DailyIncreasingBenefit doSettleTransaction(SettleContext context) {


        long ts = System.currentTimeMillis();

        DailyIncreasingBenefit dailyBenefit = context.getSettleRule().calculateDailyIncreasingBenefit(
                context.getDailySum(),
                Lazy.of(() -> {

                    // 如果今天是1-14那么返回的 '01-01' ~ '01-13'
                    return context.getDailySum()
                            .logdateRangeBeforeThisDayOfCurrentMonth()
                            .map(range -> {
                                // 无论是否结算，都归入月流水, 同时放在事务中是为了防止幻读
                                return iaaSettlementRepository.selectAllByAppIdTrafficTypeAndLogdateRange(
                                                context.getDailySum().getAppType().name(),
                                                context.getDailySum().getAppId(),
                                                IaaTrafficType.natural.name(),
                                                range._1,
                                                range._2
                                        ).stream().map(IaaSettlementPo::getIncomeAmt)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            })
                            .orElse(BigDecimal.ZERO);
                }));

        try {

            // TODO 时间的选择
            context.getBusinessEntity()
                    .orElseThrow(() -> {
                        log.error("Fail to query business entity info by appId={}", context.getDailySum().getAppId());
                        return new SettlementRetryableException("未提供商业主体无法进行结算")
                                .setSettlementStatus(IaaSettlementStatus.failed);
                    })
                    .validateContractTime(LocalDateTime.now());


            this.increaseWithdrawBill(context, dailyBenefit);

            this.increaseAppAccount(context, dailyBenefit);

            iaaSettlementRepository.updateByPrimaryKeySelective(
                    SettlementModelConvertor.convertor.toPo(new IaaSettlement()
                            .setId(context.getSettlement().getId())
                            .setWithdrawAmt(dailyBenefit.getWithdraw())
                            .setCrmChargeAmt(dailyBenefit.getCrmCharge())
                            .setSettleTime(new Date())
                            .setSettleStatus(IaaSettlementStatus.settled)
                            .setSettleReason(IaaSettlementStatus.settled.getDesc())
                            .setExtra(
                                    JsonUtil.writeValueAsString(new SettleExtra().setRule(context.getSettleRule()))
                            ))
            );


        } catch (SettlementRetryableException e) {

            log.error("Accept excepted failure, data={}, ", context, e);

            iaaSettlementRepository.updateByPrimaryKeySelective(
                    SettlementModelConvertor.convertor.toPo(new IaaSettlement()
                            .setId(context.getSettlement().getId())
                            .setSettleStatus(e.getSettlementStatus())
                            .setSettleReason(e.getMessage())));

        } catch (Throwable t) {
            log.error("Fail to finish daily settlement transaction, try rollback , data={}, ", context, t);
            throw t;
        }

        log.info("Complete daily settlement, data={}, cost={}ms", context, System.currentTimeMillis() - ts);

        return dailyBenefit;
    }

    private void increaseAppAccount(SettleContext context, DailyIncreasingBenefit dailyBenefit) {

        iaaAppAccountRepository.increaseByPrimaryKey(

                SettlementModelConvertor.convertor.toPo(new IaaAppAccount().setId(context.getAppAccount().getId())
                        .setIncomeAmt(dailyBenefit.getIncome())
                        .setIncomeBusinessPartAmt(dailyBenefit.getIncomeBusinessPart())
                        .setIncomeNaturalPartAmt(dailyBenefit.getIncomeNaturalPart())
                        .setWithdrawAmt(dailyBenefit.getWithdraw())
                        .setWithdrawBusinessPartAmt(dailyBenefit.getWithdrawBusinessPart())
                        .setWithdrawNaturalPartAmt(dailyBenefit.getWithdrawNaturalPart())
                        .setCrmChargeAmt(dailyBenefit.getCrmCharge())
                        .setSettleTimes(1)
                        .setLatestSettleTime(new Date()))
        );

    }

    private void increaseWithdrawBill(SettleContext context, DailyIncreasingBenefit dailyBenefit) {

        if (context.getNearestSettlingWithdrawBill().getBillStatus() != IaaWithdrawBillStatus.init) {
            throw new IllegalArgumentException(
                    String.format("当前账单日[%s]账单已经在[%s]状态，无法累计金额，请检查,id=[%s]",
                            context.getNearestSettlingWithdrawBill().getWithdrawDate(),
                            context.getNearestSettlingWithdrawBill().getBillStatus().name(),
                            context.getNearestSettlingWithdrawBill().getId())
            );
        }

        iaaWithdrawBillRepository.increaseByPrimaryKey(
                SettlementModelConvertor.convertor.toPo(new IaaWithdrawBill()
                        .setId(context.getNearestSettlingWithdrawBill().getId())
                        .setIncomeAmt(dailyBenefit.getIncome())
                        .setIncomeBusinessPartAmt(dailyBenefit.getIncomeBusinessPart())
                        .setIncomeNaturalPartAmt(dailyBenefit.getIncomeNaturalPart())
                        .setWithdrawAmt(dailyBenefit.getWithdraw())
                        .setWithdrawBusinessPartAmt(dailyBenefit.getWithdrawBusinessPart())
                        .setWithdrawNaturalPartAmt(dailyBenefit.getWithdrawNaturalPart())
                        .setCrmChargeAmt(dailyBenefit.getCrmCharge())
                        .setSettleTimes(1)
                        .setLatestSettleTime(new Date()))
        );

    }


    public void chargeCrmBill(
            BigDecimal crmCharge, String logdate,
            Optional<Long> accountId, IaaCrmChargeBill crmChargeBill) {

        if (crmChargeBill.isChargeSuccess()) {
            log.info("No need to charge crm, already success, data={}", crmChargeBill);
            return;
        }


        try {

            if (accountId.isEmpty()) {
                throw new IllegalArgumentException("未绑定商业账户");
            }

            ((IaaSettlementServiceImpl) AopContext.currentProxy())
                    .innerCrmChargeTransaction(
                            crmCharge, logdate, accountId, crmChargeBill);

        } catch (Exception e) {

            IaaCrmChargeBillStatus chargeBillStatus = IaaCrmChargeBillStatus.failed;
            String reason = e.getMessage();

            if (Objects.equals(chargeBillStatus, crmChargeBill.getBillStatus())
                    && Objects.equals(reason, crmChargeBill.getReason())
                    && Objects.equals(crmCharge, crmChargeBill.getChargeAmt())

            ) {

                log.warn("No change for crm charge bill, data={}", crmChargeBill);
                return;
            } else {

                iaaCrmChargeBillRepository.updateByPrimaryKeySelective(
                        SettlementModelConvertor.convertor.toPo(new IaaCrmChargeBill().setId(crmChargeBill.getId())
                                .setChargeAmt(crmCharge)
                                .setBillStatus(chargeBillStatus)
                                .setReason(reason)));
            }
        }
    }


    @Transactional("miniAppTransactionManager")
    public void innerCrmChargeTransaction(
            BigDecimal crmCharge,
            String logdate,
            Optional<Long> accountId,
            IaaCrmChargeBill crmChargeBill) {

        IaaCrmChargeBillStatus chargeBillStatus = IaaCrmChargeBillStatus.success;
        String reason = "success";

        iaaCrmChargeBillRepository.updateByPrimaryKeySelective(
                SettlementModelConvertor.convertor.toPo(new IaaCrmChargeBill().setId(crmChargeBill.getId())
                                .setChargeAmt(crmCharge)
                                .setBillStatus(chargeBillStatus)
                                .setReason(reason)
                                .setChargeTime(new Date())
//                            .setWithdrawDate(withdrawDate)
                ));

        // 现在的做法是将crm-charge服务封装为事务
        Try.run(() -> crmChargeService.charge(accountId.get(),
                        crmCharge,
                        String.format(iaaSettlementConfig.getCrmChargeBillTitle(), logdate)
                ))
                .onFailure(t -> log.error("Fail to charge crm, account={}, crmCharge={}", accountId, crmCharge, t))
                .get();


    }


    /**
     * com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#onMonthlyWithdrawDateSchedule(null)
     *
     * @param withdrawDateLte
     */
    @Override
    public void onMonthlyWithdrawDateSchedule(String withdrawDateLte) {

        // 1-16 调用最多访问 1-15的账单
        final String fWithdrawDateLte = Optional.ofNullable(withdrawDateLte)
                .orElse(WithdrawDate.generateNextNearestWithdrawDateByDateTime(
                        LocalDateTime.now().minusDays(1)).getWithdrawDate());

        Iterator<List<IaaWithdrawBill>> iterator = IteratorHelper.buildIterator(
                (id, limit) -> {
                    return iaaWithdrawBillRepository.selectAllByIdGtAndLimitAndStatusInAndWithdrawDateLte(
                            id, limit, Lists.newArrayList(IaaWithdrawBillStatus.init.name()),
                            fWithdrawDateLte
                    ).stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());
                },
                IaaWithdrawBill::getId,

                iaaSettlementConfig.getScanHistoryWithdrawBillBatchSize()
        );

        while (iterator.hasNext()) {

            List<IaaWithdrawBill> bills = iterator.next();

            bills.stream().collect(Collectors.groupingBy(bill -> Tuple.of(bill.getAppType(), bill.getAppId())))
                    .forEach((key, value) -> {
                        this.onMonthlyWithdraw4SingleApp(SettlementModelConvertor.convertor.bill2MonthlyEvent(value.get(0)));
                    });

        }

        log.info("Complete monthly withdraw date schedule, data={}", withdrawDateLte);


    }

    /**
     * Q：该事件由谁调度？berseker? xxl？ A: 事实上都是可以的，最终选择xxl进行调度。仅作无状态的调度某种程度上更加简洁。
     * <p>
     * 一个很显然的原因 半月结时间必须要保证发生在daily事件只有，保证15号当天的计算任务已经完成。
     * 但是又不能直接用15号当天的事件驱动，因为可能有app的交易正好15号当天是没有流水的（虽然较少)
     * <p>
     * backdoor:
     * <p>
     * <p>
     * com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#onMonthlyWithdraw4SingleApp({"appType": "mini_game","appId": "123"})
     *
     * @param halfMonthEvent
     */
    public void onMonthlyWithdraw4SingleApp(IaaWithdrawMonthlyEvent4SingleApp halfMonthEvent) {

        // 今天为16号, 尝试将init的账单转为withdrawable

        WithdrawDate wantedWithdrawDate = WithdrawDate.generateNextNearestWithdrawDateByDateTime(
                LocalDateTime.now().minusDays(1));


        List<IaaWithdrawBill> settlingBills = iaaWithdrawBillRepository
                .selectByAppTypAppKeyAndStatusAndWithdrawDateLte(
                        halfMonthEvent.getAppType().name(), halfMonthEvent.getAppId(),
                        IaaWithdrawBillStatus.init.name(),
                        wantedWithdrawDate.getWithdrawDate()
                ).stream()
                .map(SettlementModelConvertor.convertor::fromPo)
                .collect(Collectors.toList());

        this.turnWithdrawable4BillsGroupingByApp(settlingBills);

    }

    public void onWithdrawDateAndAppIds(IaaAppType appType, String withdrawDate, List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            log.warn("No appIds to process, skip");
            return;
        }
        List<IaaWithdrawBillPo> pos = iaaWithdrawBillRepository.selectByAppTypeAppIdsAndWithdrawDate(appType.name(), appIds, withdrawDate);
        if (CollectionUtils.isEmpty(pos)) {
            log.warn("No withdraw bill found, appType={}, withdrawDate={}, appIds={}", appType, withdrawDate, appIds);
            return;
        }
        List<IaaWithdrawBill> bills = pos.stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());
        log.info("onWithdrawDateAndAppIds, appType={}, withdrawDate={}, appIds={}", appType, withdrawDate, appIds);
        Lists.partition(bills, 8).forEach(partition -> {
            try {
                ForkJoinUtil.runnableForkJoin(partition.stream().map(bill -> (Runnable) () -> {
                    try {
                        this.turnWithdrawable(bill);
                    } catch (Throwable t) {
                        log.error("Fail to turn withdrawable, data={}", bill, t);
                    }
                }).collect(Collectors.toList()), iaaSettlementThreadPoolExecutor);
            } catch (Throwable ex) {
                log.error("Fail to handle partition={} ", partition, ex);
            }
        });
        log.info("onWithdrawDateAndAppIds, appType={}, withdrawDate={}, appIds={} done", appType, withdrawDate, appIds);
    }

    public void onWithdrawDate(IaaAppType appType, String withdrawDate) {
        List<IaaWithdrawBillPo> pos = iaaWithdrawBillRepository.selectByAppTypeAppIdsAndWithdrawDate(appType.name(), null, withdrawDate);
        if (CollectionUtils.isEmpty(pos)) {
            log.warn("No withdraw bill found, appType={}, withdrawDate={}, appIds={}", appType, withdrawDate);
            return;
        }
        List<IaaWithdrawBill> bills = pos.stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());
        List<String> appIds = bills.stream().map(IaaWithdrawBill::getAppId).collect(Collectors.toList());
        log.info("onWithdrawDate, appType={}, withdrawDate={}, appIds={}", appType, withdrawDate, appIds);
        Lists.partition(bills, 8).forEach(partition -> {
            try {
                ForkJoinUtil.runnableForkJoin(partition.stream().map(bill -> (Runnable) () -> {
                    try {
                        this.turnWithdrawable(bill);
                    } catch (Throwable t) {
                        log.error("Fail to turn withdrawable, data={}", bill, t);
                    }
                }).collect(Collectors.toList()), iaaSettlementThreadPoolExecutor);
            } catch (Throwable ex) {
                log.error("Fail to handle partition={} ", partition, ex);
            }
        });
        log.info("onWithdrawDateAndAppIds, appType={}, withdrawDate={}, appIds={} done", appType, withdrawDate, appIds);
    }


    private void turnWithdrawable4BillsGroupingByApp(List<IaaWithdrawBill> input) {

        WithdrawDate wantedWithdrawDate = WithdrawDate.generatePreviousDateOfCurrentDate(LocalDate.now());

        List<IaaWithdrawBill> settlingBills = input.stream()
                // 二次确认，这是最低的底线， 大于结算日的不能被提现，避免提现被创建的账单被提现
                .filter(bill -> bill.getWithdrawDate().compareTo(wantedWithdrawDate.getWithdrawDate()) <= 0)
                .filter(bill -> bill.getBillStatus().canTurnWithdrawable())
                // 根据时间倒序排序
                .sorted((a, b) -> b.getWithdrawDate().compareTo(a.getWithdrawDate()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(settlingBills)) {
            log.error("No withdraw bill can turn withdrawable, data={}", settlingBills);
            return;
        }

        if (settlingBills.size() > 1) {
            // 及其严重的问题，理论上只发生于每月的提现任务发生了报错
            log.error("Ugly error! More than one active withdraw bill found, data={}", settlingBills.size());

        }

        if (!Objects.equals(settlingBills.get(0).getWithdrawDate(), wantedWithdrawDate.getWithdrawDate())) {
            log.error("Ugly error! Latest withdraw bill is not the wanted withdraw date, bill={}, expected={}",
                    settlingBills.get(0), wantedWithdrawDate);
        }

        settlingBills.forEach(this::turnWithdrawable);

    }


    /**
     * 转为可提现
     */
    public void turnWithdrawable(IaaWithdrawBill bill) {

        IaaWithdrawBill formSelective = new IaaWithdrawBill().setId(bill.getId());

        if (BigDecimal.ZERO.compareTo(bill.getWithdrawAmt()) == 0) {
            log.error("Ugly error! bill withdrawAmt is zero, why bill exist?, data={}", bill);
            return;
        }

        try {
            BusinessEntityInfo businessEntityInfo = miniGameBusinessInfoProvider.queryBusinessEntityInfoByAppId(
                            bill.getAppId())
                    .orElseThrow(() -> new IllegalArgumentException("未提供商业主体无法进行提现"));

            String period = Try.of(() -> bill.getWithdrawDate().substring(0, 6)).getOrElse("");

            String periodAfterReplacement = Optional.ofNullable(iaaSettlementConfig.getAccrualPeriodMagicReplacement())
                    .map(map -> map.get(period))
                    .orElse(period);

            // 预提单
            AccrualCreateMandatoryParams param = new AccrualCreateMandatoryParams()
                    .setBusinessCode(
                            String.format(iaaSettlementConfig.getAccrualBusinessCodeTpl(),
                                    snowFlakeIdGenerator.nextId()))
                    .setPayee(businessEntityInfo.getPayee())
                    .setAmtInCny(bill.getWithdrawAmt().divide(new BigDecimal(100), 2))
                    .setPeriod(periodAfterReplacement);

            HuilianyiAccrualCreateResult accrual = huilianyiPaymentService.createAccrual(param);

            formSelective.setAccrualId(accrual.getKey())
                    .setBillStatus(IaaWithdrawBillStatus.withdrawable)
                    .setFailReason(IaaWithdrawBillStatus.withdrawable.getDesc())
                    .setAccrualExtra(JsonUtil.writeValueAsString(new AccrualExtra()
                            .setParams(param)
                            .setAccrualCode(accrual.getErrorCode())
                            .setAccrualMessage(accrual.getMessage()))
                    )
                    .setBusinessEntityName(businessEntityInfo.getBusinessEntityName())
                    .setExtra(JsonUtil.writeValueAsString(new BillExtra()
                            .setTaxRate(iaaSettlementConfig.getTaxRate())
                            .setTaxAmt(bill.getWithdrawAmt()
                                    .multiply(new BigDecimal(iaaSettlementConfig.getTaxRate()))
                                    .setScale(2, RoundingMode.HALF_UP)
                            )
                            .setWithdrawAmtAfterTax(
                                    bill.getWithdrawAmt().subtract(
                                            bill.getWithdrawAmt()
                                                    .multiply(new BigDecimal(iaaSettlementConfig.getTaxRate()))
                                                    .setScale(2, RoundingMode.HALF_UP)
                                    ))));

        } catch (Throwable t) {

            log.error("Fail to turn bill to withdrawable, data={}", bill, t);

            formSelective.setBillStatus(IaaWithdrawBillStatus.turn_withdrawable_failed)
                    .setFailReason(t.getMessage());

        }

        this.updateWithdrawBillWithNotification(formSelective);

        log.info("Success to handle withdraw bill, data={}, form={}", bill, formSelective);


    }


    /**
     *
     com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#applyWithdraw({"appType":"mini_game","appId":"biligame82833db7ab871bd8","billIds":[4],"applyReason":"提现","invoiceImgUrls":["http://i0.hdslb.com/bfs/sycp/mgk/collage/png/202503/be57da6192227f0057780834cc2fd8e2.png"],"skipCheckInvoice":true})
     com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementServiceImpl#applyWithdraw({"appType":"mini_game","appId":"biligameb3c74c18663dba06","billIds":[5],"applyReason":"提现","invoiceImgUrls":["http://i0.hdslb.com/bfs/sycp/mgk/collage/png/202503/be57da6192227f0057780834cc2fd8e2.png"],"skipCheckInvoice":true})
     * @param applyRequest 申请提现请求， 包含app信息，账单信息，发票
     * @return
     */
    @Override
    public WithdrawApplyResult applyWithdraw(WithdrawApplyRequest applyRequest) {

        Lazy<List<IaaWithdrawBill>> billsLazy = Lazy.of(() -> iaaWithdrawBillRepository.selectAllByPrimaryKeys(
                        applyRequest.getBillIds()).stream().map(SettlementModelConvertor.convertor::fromPo)
                .collect(Collectors.toList()));

        Lazy<Map<String, HuilianyiInvoiceWrapper>> invoiceLazy = Lazy.of(
                () -> huilianyiPaymentService.batchProcessInvoice(applyRequest.getInvoiceImgUrls()));

        Lazy<BusinessEntityInfo> businessEntityLazy = Lazy.of(
                () -> miniGameBusinessInfoProvider.queryBusinessEntityInfoByAppId(
                                applyRequest.getAppId())
                        .orElseThrow(() -> new IllegalArgumentException("未提供商业主体无法进行提现")));

        Lazy<Map<Long, List<IaaSettlement>>> settlementsLazy = Lazy.of(() -> iaaSettlementRepository.selectAllByWithdrawBillId(applyRequest.getBillIds()).stream()
                .map(SettlementModelConvertor.convertor::fromPo)
                .collect(Collectors.groupingBy(IaaSettlement::getWithdrawBillId)));

        Tuple3<List<IaaWithdrawBill>, List<IaaWithdrawBill>, Map<String, HuilianyiInvoiceWrapper>> withdrawAndSkipBills =
                applyRequest.validate(billsLazy, invoiceLazy, businessEntityLazy, settlementsLazy, iaaSettlementConfig);

        List<IaaWithdrawBill> bills = withdrawAndSkipBills._1;

        Map<String, HuilianyiInvoiceWrapper> ocrResults = invoiceLazy.get();

        ExpenseCreateMandatoryParams params = new ExpenseCreateMandatoryParams()
                .setInvoiceImgUrls(applyRequest.getInvoiceImgUrls())
                .setPayee(businessEntityLazy.get().getPayee())
                .setContractNumber(businessEntityLazy.get().getContractNumber())
                .setBankNumber(businessEntityLazy.get().getBankAccountNumber())
                .setBusinessCode(
                        String.format(iaaSettlementConfig.getExpenseBusinessCodeTpl(), snowFlakeIdGenerator.nextId()))
                .setOcrResults(ocrResults.entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> SimpleOcrResult.fromInvoiceWrapper(entry.getValue()),
                        (a, b) -> a
                )))
                .setWithdrawAmtInCny(bills.stream()
                        .map(IaaWithdrawBill::getWithdrawAmt)
                        // 先转成元，再相加
                        .map(amt -> amt.divide(new BigDecimal(100), RoundingMode.HALF_UP))
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .setAccrualId2AmtInCny(bills.stream().collect(
                        Collectors.toMap(IaaWithdrawBill::getAccrualId,
                                bill -> bill.getWithdrawAmt().divide(new BigDecimal(100), RoundingMode.HALF_UP),
                                (a, b) -> a)));

        this.updateBatchWithdrawBillWithNotification(
                new IaaWithdrawBill()
                        .setBillStatus(IaaWithdrawBillStatus.withdrawing)
                        .setFailReason("提交中")
                        .setWithdrawApplyTime(new Date())
                        .setInvoiceImgUrl(applyRequest.getJoinedInvoiceImgUrl())
                        .setExpenseExtra(JsonUtil.writeValueAsString(new ExpenseExtra()
                                .setParams(params))),
                bills.stream().map(IaaWithdrawBill::getId).collect(Collectors.toList()));

        CompletableFuture.supplyAsync(() -> {
            HuilianyiExpenseCreateResult expense = huilianyiPaymentService.createExpense(params);

            return Tuple.of(params, expense);

        }, iaaSettlementThreadPoolExecutor).whenComplete((r, t) -> {

            IaaWithdrawBill formSelective = new IaaWithdrawBill();

            if (t == null) {
                formSelective.setBillStatus(IaaWithdrawBillStatus.withdrawing)
                        .setFailReason("审批中")
                        .setExpenseId(r._2.getKey())
                        .setExpenseCode(r._2.getErrorCode())
                        .setExpenseMessage(r._2.getMessage())
                        .setWithdrawApplyAmt(bills.stream()
                                .map(IaaWithdrawBill::getWithdrawAmt)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                        .setExpenseExtra(JsonUtil.writeValueAsString(new ExpenseExtra()
                                .setParams(params)
                                .setExpenseCode(r._2.getErrorCode())
                                .setExpenseMessage(r._2.getMessage())
                        ));

                log.info("Success to create expense, now waiting callback of huilianyi. params={}, data={}, bill={}",
                        r._1, r._2, applyRequest);

            } else {
                log.error("Fail to apply withdraw, when creating expense, data={}, bills={} ",
                        applyRequest, withdrawAndSkipBills._1, t);

                String errorCode = "";
                String errorMessage = Optional.of(t).map(Throwable::getCause).orElse(t).getMessage();
                if (t instanceof WithdrawRetryableException) {
                    errorCode = ((WithdrawRetryableException) t).getExpenseErrorCode();
                    errorMessage = ((WithdrawRetryableException) t).getExpenseMessage();
                }

                formSelective.setBillStatus(IaaWithdrawBillStatus.failed)
                        .setExpenseCode(errorCode)
                        .setFailReason(errorMessage)
                        .setExpenseExtra(JsonUtil.writeValueAsString(new ExpenseExtra()
                                .setParams(params)
                                .setExpenseCode(errorCode)
                                .setExpenseMessage(errorMessage)
                        ));

            }

            this.updateBatchWithdrawBillWithNotification(
                    formSelective,
                    bills.stream().map(b -> b.getId()).collect(Collectors.toList())
            );

        });


        log.info("Success to apply withdraw, data={}, bills={}", applyRequest, withdrawAndSkipBills._1);

        return new WithdrawApplyResult()
                .setApplySuccessBillIds(
                        withdrawAndSkipBills._1.stream().map(
                                IaaWithdrawBill::getId).collect(Collectors.toList()))
                .setSkipBillIds(
                        withdrawAndSkipBills._2.stream().map(
                                IaaWithdrawBill::getId).collect(Collectors.toList()));

    }


    @Override
    public void listenHuilianyiCallback(HuilianyiExpenseCallback callback) {

        log.info("Accept huilianyi callback, data={}", callback);

        // 需要注意的是，由于存在批量提现的行为，一次汇联易的付款单，可能关联多个小程序结算单。需要更新多个状态

        if (StringUtils.isEmpty(callback.getBusinessCode()) ||
                StringUtils.isEmpty(callback.getExpenseStatus())) {
            log.error("Invalid huilianyi callback, businessCode is empty, data={}", callback);
            return;
        }

        if (!callback.getBusinessCode().startsWith(iaaSettlementConfig.getExpenseBusinessCodePrefix())) {
            log.error("Invalid huilianyi callback, businessCode is not match, data={}", callback);
            return;
        }


        List<IaaWithdrawBill> bills = iaaWithdrawBillRepository
                .selectByExpenseId(callback.getBusinessCode())
                .stream()
                .map(SettlementModelConvertor.convertor::fromPo)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bills)) {
            log.info("No withdraw bill found by expenseId, data={}, skip it", callback);
            return;
        }

        IaaWithdrawBillStatus updateTargetBillStatus = null;

        String failReason = null;

        if (callback.isSuccess()) {
            updateTargetBillStatus = IaaWithdrawBillStatus.success;
            failReason = IaaWithdrawBillStatus.success.getDesc();
        } else if (callback.isFail()) {
            updateTargetBillStatus = IaaWithdrawBillStatus.failed;
            failReason = callback.getPayFailReason();
        }
        else {
            log.warn("Unknown status of huilianyi callback, data={}", callback);
            // 保持原状
            updateTargetBillStatus = null;
            failReason = "提现状态回调: " + callback.getExpenseStatus();
        }


        List<Long> billIds = bills.stream().map(IaaWithdrawBill::getId).collect(Collectors.toList());

        this.updateBatchWithdrawBillWithNotification(
                new IaaWithdrawBill()
                        .setBillStatus(updateTargetBillStatus)
                        .setFailReason(failReason)
                        .setExpenseCode(callback.getExpenseStatus())
                        .setExpenseMessage(callback.getPayFailReason()),
                billIds);

        log.info("Success to update withdraw bill status by huilianyi callback, data={}, billIds={}",
                callback, billIds);

    }


    private void updateWithdrawBillWithNotification(IaaWithdrawBill formSelective) {

        iaaWithdrawBillRepository.updateByPrimaryKeySelective(
                SettlementModelConvertor.convertor.toPo(formSelective));

        // TODO 通知商家业务平台
        if (formSelective.getBillStatus() != null) {

            applicationEventPublisher.publishEvent(
                    new WithdrawBillStatusUpdateEvent(this).setWithdrawBillId(formSelective.getId())
            );
        }
    }


    private void updateBatchWithdrawBillWithNotification(IaaWithdrawBill formSelective, List<Long> billIds) {

        iaaWithdrawBillRepository.updateBatchByPrimaryKeysSelective(
                SettlementModelConvertor.convertor.toPo(formSelective), billIds
        );

        if (formSelective.getBillStatus() != null) {

            billIds.forEach(id -> applicationEventPublisher.publishEvent(
                    new WithdrawBillStatusUpdateEvent(this).setWithdrawBillId(id)));

        }

    }
}
