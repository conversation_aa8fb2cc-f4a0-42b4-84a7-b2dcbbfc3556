package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;


import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenAppletSeasonDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletSeasonPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletSeasonPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppAppletSeasonRepository;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: huan
 * @Date: 2025-06-27日 15:38
 * @Description:
 */
@Repository
public class MiniAppAppletSeasonRepository implements IMiniAppAppletSeasonRepository {

    @Resource
    private MiniAppOpenAppletSeasonDao appletSeasonDao;

    @Override
    public String getSeasonBindingApplet(Long seasonId) {
        MiniAppOpenAppletSeasonPoExample example = new MiniAppOpenAppletSeasonPoExample();
        example.or().andSeasonIdEqualTo(seasonId);
        List<MiniAppOpenAppletSeasonPo> seasonPos = appletSeasonDao.selectByExample(example);
        if (CollectionUtils.isEmpty(seasonPos)) {
            return null;
        }
        return seasonPos.get(0).getAppId();
    }

    @Override
    public Map<Long, String> getSeasonBindingApplets(List<Long> seasonIds) {
        MiniAppOpenAppletSeasonPoExample example = new MiniAppOpenAppletSeasonPoExample();
        example.or().andSeasonIdIn(seasonIds);
        List<MiniAppOpenAppletSeasonPo> seasonPos = appletSeasonDao.selectByExample(example);
        if (CollectionUtils.isEmpty(seasonPos)) {
            return Map.of();
        }
        return seasonPos.stream().collect(Collectors.toMap(MiniAppOpenAppletSeasonPo::getSeasonId, MiniAppOpenAppletSeasonPo::getAppId));
    }

    @Override
    public void bindSeasonToApplet(Long seasonId, String appId) {
        // 先查询是否存在
        String bindingApplet = getSeasonBindingApplet(seasonId);
        // 如果已经绑定了小程序，并且绑定的小程序与传入的小程序不一致，则更新
        if (StringUtils.isBlank(bindingApplet)) {
            // 如果没有绑定，则插入新的绑定关系
            MiniAppOpenAppletSeasonPo po = new MiniAppOpenAppletSeasonPo();
            po.setSeasonId(seasonId);
            po.setAppId(appId);
            appletSeasonDao.insertSelective(po);
        } else if (!StringUtil.equals(bindingApplet, appId)) {
            // 如果已经绑定了小程序，并且与传入的小程序不一致，则更新
            MiniAppOpenAppletSeasonPo po = new MiniAppOpenAppletSeasonPo();
            po.setAppId(appId);
            MiniAppOpenAppletSeasonPoExample example = new MiniAppOpenAppletSeasonPoExample();
            example.or().andSeasonIdEqualTo(seasonId);
            appletSeasonDao.updateByExampleSelective(po, example);
        }
    }

    @Override
    public List<Long> getSeasonIdsByAppId(String appId) {
        MiniAppOpenAppletSeasonPoExample example = new MiniAppOpenAppletSeasonPoExample();
        example.or().andAppIdEqualTo(appId);
        List<MiniAppOpenAppletSeasonPo> seasonPos = appletSeasonDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(seasonPos)) {
            return seasonPos.stream()
                    .map(MiniAppOpenAppletSeasonPo::getSeasonId)
                    .collect(Collectors.toList());
        }
        // 如果没有绑定的剧集，则返回空列表
        return List.of();
    }
}
