package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletPriorityPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletPriorityPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAppletPriorityDao {
    long countByExample(MiniAppOpenAppletPriorityPoExample example);

    int deleteByExample(MiniAppOpenAppletPriorityPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAppletPriorityPo record);

    int insertBatch(List<MiniAppOpenAppletPriorityPo> records);

    int insertUpdateBatch(List<MiniAppOpenAppletPriorityPo> records);

    int insert(MiniAppOpenAppletPriorityPo record);

    int insertUpdateSelective(MiniAppOpenAppletPriorityPo record);

    int insertSelective(MiniAppOpenAppletPriorityPo record);

    List<MiniAppOpenAppletPriorityPo> selectByExample(MiniAppOpenAppletPriorityPoExample example);

    MiniAppOpenAppletPriorityPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAppletPriorityPo record, @Param("example") MiniAppOpenAppletPriorityPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAppletPriorityPo record, @Param("example") MiniAppOpenAppletPriorityPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAppletPriorityPo record);

    int updateByPrimaryKey(MiniAppOpenAppletPriorityPo record);
}