package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/
public interface MiniAppOpenSeasonAuthorizationExtDao {

    List<SeasonAuthorizationResultBo> selectSeasonAuthedBySeasonId(@Param(value = "seasonIdList") List<Long> seasonIdList);

    List<String> selectDistinctAppIds();

    List<Long> selectDistinctMids();
}
