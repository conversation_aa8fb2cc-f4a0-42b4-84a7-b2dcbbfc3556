package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;


import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenAppletPriorityDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletPriorityPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletPriorityPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppAppletPriorityRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: huan
 * @Date: 2025-06-30 21:10
 * @Description:
 */
@Repository
public class MiniAppAppletPriorityRepository implements IMiniAppAppletPriorityRepository {

    @Resource
    private MiniAppOpenAppletPriorityDao miniAppOpenAppletPriorityDao;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSavePriority(List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }
        // 删除现有的优先级记录
        MiniAppOpenAppletPriorityPoExample example = new MiniAppOpenAppletPriorityPoExample();
        miniAppOpenAppletPriorityDao.deleteByExample(example);

        // 移除无效的appId
        appIds.removeIf(String::isBlank);
        for (int i = 0; i < appIds.size(); i++) {
            String appId = appIds.get(i);
            // 设置优先级
            MiniAppOpenAppletPriorityPo po = new MiniAppOpenAppletPriorityPo();
            po.setAppId(appId);
            po.setPriority(i + 1); // 优先级从1开始
            miniAppOpenAppletPriorityDao.insert(po);
        }
    }

    @Override
    public List<String> getPriorityAppIds() {
        MiniAppOpenAppletPriorityPoExample example = new MiniAppOpenAppletPriorityPoExample();
        example.setOrderByClause("priority ASC");
        List<MiniAppOpenAppletPriorityPo> priorityPos = miniAppOpenAppletPriorityDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(priorityPos)) {
            return priorityPos.stream()
                    .map(MiniAppOpenAppletPriorityPo::getAppId)
                    .collect(Collectors.toList());
        }
        return List.of();
    }
}
