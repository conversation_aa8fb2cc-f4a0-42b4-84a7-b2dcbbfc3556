package com.bilibili.miniapp.open.repository.mysql.miniapp.repo;

import java.util.List;
import java.util.Map;

public interface IMiniAppAppletSeasonRepository {

    /**
     * 获取剧绑定的小程序
     * @param seasonId
     * @return
     */
    String getSeasonBindingApplet(Long seasonId);

    /**
     * 获取剧绑定的小程序列表
     * @param seasonIds
     * @return
     */
    Map<Long, String> getSeasonBindingApplets(List<Long> seasonIds);

    /**
     * 绑定剧集到小程序
     * @param seasonId
     * @param appId
     */
    void bindSeasonToApplet(Long seasonId, String appId);

    /**
     * 通过小程序ID获取绑定的剧集ID列表
     * @param appId
     * @return
     */
    List<Long> getSeasonIdsByAppId(String appId);
}
