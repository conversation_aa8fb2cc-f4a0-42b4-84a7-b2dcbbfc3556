package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonVideoViewSummaryPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonVideoViewSummaryPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenSeasonVideoViewSummaryDao {
    long countByExample(MiniAppOpenSeasonVideoViewSummaryPoExample example);

    int deleteByExample(MiniAppOpenSeasonVideoViewSummaryPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenSeasonVideoViewSummaryPo record);

    int insertBatch(List<MiniAppOpenSeasonVideoViewSummaryPo> records);

    int insertUpdateBatch(List<MiniAppOpenSeasonVideoViewSummaryPo> records);

    int insert(MiniAppOpenSeasonVideoViewSummaryPo record);

    int insertUpdateSelective(MiniAppOpenSeasonVideoViewSummaryPo record);

    int insertSelective(MiniAppOpenSeasonVideoViewSummaryPo record);

    List<MiniAppOpenSeasonVideoViewSummaryPo> selectByExample(MiniAppOpenSeasonVideoViewSummaryPoExample example);

    MiniAppOpenSeasonVideoViewSummaryPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenSeasonVideoViewSummaryPo record, @Param("example") MiniAppOpenSeasonVideoViewSummaryPoExample example);

    int updateByExample(@Param("record") MiniAppOpenSeasonVideoViewSummaryPo record, @Param("example") MiniAppOpenSeasonVideoViewSummaryPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenSeasonVideoViewSummaryPo record);

    int updateByPrimaryKey(MiniAppOpenSeasonVideoViewSummaryPo record);
}