package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletSeasonPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenAppletSeasonPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAppletSeasonDao {
    long countByExample(MiniAppOpenAppletSeasonPoExample example);

    int deleteByExample(MiniAppOpenAppletSeasonPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAppletSeasonPo record);

    int insertBatch(List<MiniAppOpenAppletSeasonPo> records);

    int insertUpdateBatch(List<MiniAppOpenAppletSeasonPo> records);

    int insert(MiniAppOpenAppletSeasonPo record);

    int insertUpdateSelective(MiniAppOpenAppletSeasonPo record);

    int insertSelective(MiniAppOpenAppletSeasonPo record);

    List<MiniAppOpenAppletSeasonPo> selectByExample(MiniAppOpenAppletSeasonPoExample example);

    MiniAppOpenAppletSeasonPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAppletSeasonPo record, @Param("example") MiniAppOpenAppletSeasonPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAppletSeasonPo record, @Param("example") MiniAppOpenAppletSeasonPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAppletSeasonPo record);

    int updateByPrimaryKey(MiniAppOpenAppletSeasonPo record);
}