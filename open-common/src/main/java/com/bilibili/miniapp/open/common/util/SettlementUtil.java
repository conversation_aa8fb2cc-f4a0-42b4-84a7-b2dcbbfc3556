package com.bilibili.miniapp.open.common.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
public class SettlementUtil {

    private SettlementUtil() {
    }

    private final static DateTimeFormatter yyyyMMddFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    private final static DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    /**
     * 分转元，保留2位小数
     */
    public static String convertCentToYuan(Long cent) {
        BigDecimal yuan = convertCentToYuanBigDecimal(cent);
        if (Objects.equals(yuan, BigDecimal.ZERO)) {
            return "0.00";
        }
        return yuan.toString();
    }

    public static BigDecimal convertCentToYuanBigDecimal(Long cent) {
        if (cent == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(cent).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }


    public static String toPercentage(String value) {
        if (StringUtils.isBlank(value)) {
            return "0%";
        }
        return Integer.parseInt(value) * 100 + "%";
    }

    public static String convertToDateFormat(String inputDate) {
        LocalDate date = LocalDate.parse(inputDate, yyyyMMddFormatter);
        return date.format(outputFormatter);
    }

    public static String yyyyMMdd(long timestamp) {
        LocalDate date = new Timestamp(timestamp).toLocalDateTime().toLocalDate();
        return yyyyMMddFormatter.format(date);
    }

}
