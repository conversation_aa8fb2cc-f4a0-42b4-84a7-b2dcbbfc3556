package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 税率类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@AllArgsConstructor
@Getter
public enum TaxType {

    /**
     * 一般纳税人-6%
     */
    GENERAL_TAXPAYER_6_PERCENT(1, "一般纳税人-6%", "0.06");

    private final int code;
    private final String desc;
    private final String taxRatio;

    public static TaxType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown tax type code=" + code));
    }

    public static TaxType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
