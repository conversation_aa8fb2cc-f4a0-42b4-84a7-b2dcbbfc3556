package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Getter
@AllArgsConstructor
public enum HuiLianYiPaymentStatus {

    /**
     * 101 关闭
     * 102 审批通过
     * 103 已删除
     * 104 付款失败
     * 105 付款成功
     * 106 退票
     * 107 审批驳回
     * 108 支付单失败
     * 109 支付单成功
     */
    CLOSED("101", "关闭"),
    APPROVED("102", "审批通过"),
    DELETED("103", "已删除"),
    PAYMENT_FAILED("104", "付款失败"),
    PAYMENT_SUCCESS("105", "付款成功"),
    REFUNDED("106", "退票"),
    APPROVAL_REJECTED("107", "审批驳回"),
    PAYMENT_ORDER_FAILED("108", "支付单失败"),
    PAYMENT_ORDER_SUCCESS("109", "支付单成功"),
    ;
    private final String code;
    private final String desc;

    public static HuiLianYiPaymentStatus getByCode(String code) {
        for (HuiLianYiPaymentStatus value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
