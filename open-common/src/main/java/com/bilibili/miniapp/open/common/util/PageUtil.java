package com.bilibili.miniapp.open.common.util;

import java.util.Collections;
import java.util.List;
import java.util.RandomAccess;
import java.util.stream.Collectors;

public class PageUtil {

    // 优化分页算法 - 避免全量遍历
    // sourceList已经是排序好的数据了
    public static <T> List<T> getSubListWithWindow(List<T> sourceList, int pageNum, int pageSize) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }

        int totalSize = sourceList.size();
        int startIndex = (pageNum - 1) * pageSize;

        // 边界保护
        if (startIndex >= totalSize) {
            return Collections.emptyList();
        }

        int endIndex = Math.min(startIndex + pageSize, totalSize);

        // 优化方案1：针对ArrayList直接sublist（时间复杂度O(1)）
        if (sourceList instanceof RandomAccess) {
            return sourceList.subList(startIndex, endIndex);
        }

        // 优化方案2：使用流式分页（解决LinkedList等链表结构遍历性能问题）
        return sourceList.stream()
                .skip(startIndex)
                .limit(pageSize)
                .collect(Collectors.toList());
    }
}
