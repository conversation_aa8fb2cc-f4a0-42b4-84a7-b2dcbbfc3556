package com.bilibili.miniapp.open.common.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Data
public class Page implements Serializable {

    private static final long serialVersionUID = 1343009292412578816L;

    public final static int DEFAULT_OFFSET = 0;
    public final static int DEFAULT_PAGE = 1;
    public final static int DEFAULT_LIMIT = 20;
    public final static int MAX_LIMIT = 100;
    private int limit;
    private int offset;
    private int page;
    private int pageSize;

    public Page() {
        this.limit = DEFAULT_LIMIT;
        this.offset = DEFAULT_OFFSET;
        this.page = DEFAULT_PAGE;
        this.pageSize = DEFAULT_LIMIT;
    }

    private Page(int offset, int limit, int page, int pageSize) {
        this.limit = limit;
        this.offset = offset;
        this.page = page;
        this.pageSize = pageSize;
    }

    public static Page valueOf(Page page) {
        return (null == page) ? new Page() : page;
    }

    private static boolean isPageConditionPassing(int page, int pageSize) {
        return page >= 0 && pageSize >= 0;
    }

    public static Page valueOf(int page, int pageSize) {
        return isPageConditionPassing(page, pageSize) ? new Page((page - 1) * pageSize, pageSize, page, pageSize) :
                new Page();
    }
}

