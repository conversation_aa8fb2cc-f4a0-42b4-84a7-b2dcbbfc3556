package com.bilibili.miniapp.open.portal.mapper;


import com.bilibili.miniapp.api.dto.applet.AppletAuthorizationAuthorDto;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: huan
 * @Date: 2025-07-01 14:28
 * @Description:
 */

@Mapper
public interface AppletPlatformControllerMapper {

    AppletPlatformControllerMapper INSTANCE = Mappers.getMapper(AppletPlatformControllerMapper.class);

    @Mapping(source = "name", target = "nickName")
    AppletAuthorizationAuthorDto toAppletAuthorizationAuthorDto(UserInfoBo bo);
}
