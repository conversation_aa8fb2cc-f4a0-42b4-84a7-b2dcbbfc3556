package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.IncomeControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.income.IncomeDetailVo;
import com.bilibili.miniapp.open.portal.vo.income.IncomeSummaryVo;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * 收入相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/income")
public class IncomeController extends AbstractController {

    @Autowired
    private IIncomeService incomeService;

    /**
     * 获取收入汇总
     */
    @GetMapping("/summary")
    @MainSiteLoginValidation
    public Response<IncomeSummaryVo> getIncomeSummary(Context context) {

        IncomeSummaryBo incomeSummary = incomeService.getIncomeSummary(context.getMid());

        return Response.SUCCESS(IncomeControllerMapper.MAPPER.boToVo(incomeSummary));
    }

    /**
     * 获取收入明细
     */
    @GetMapping("/list")
    @MainSiteLoginValidation
    public Response<PageResult<IncomeDetailVo>> getIncomeDetails(Context context,
                                                                 @RequestParam(value = "app_id", required = false) String appId,
                                                                 @RequestParam(value = "app_name", required = false) String appName,
                                                                 @RequestParam(value = "begin_time", required = false) Long beginTime,
                                                                 @RequestParam(value = "end_time", required = false) Long endTime,
                                                                 @RequestParam(value = "traffic_type", required = false) Integer trafficType,
                                                                 @RequestParam(value = "withdraw_status", required = false) Integer withdrawStatus,
                                                                 @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                 @RequestParam(value = "size", defaultValue = "10") Integer size) {

        PageResult<IncomeDetailBo> pageResult = incomeService.queryIncomeDetails(
                context.getMid(),
                appId,
                appName,
                beginTime,
                endTime,
                trafficType,
                withdrawStatus,
                page,
                size
        );

        return Response.SUCCESS(
                PageResult.<IncomeDetailVo>builder()
                        .records(pageResult.getRecords().stream()
                                .map(IncomeControllerMapper.MAPPER::boToVo)
                                .collect(Collectors.toList()))
                        .total(pageResult.getTotal())
                        .build()
        );
    }
}
