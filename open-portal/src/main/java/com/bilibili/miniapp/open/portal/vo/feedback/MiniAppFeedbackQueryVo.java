package com.bilibili.miniapp.open.portal.vo.feedback;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
@Schema(description = "查询反馈")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppFeedbackQueryVo {
    @Schema(description = "用户账号mid, 精确查询")
    private Long mid;

    @Schema(description = "用户昵称，模糊查询")
    private String nickName;

    @Schema(description = "反馈子类型，1-小程序闪退，2-卡顿，3-黑屏白屏，4-死机，5-界面错位，6-界面加载慢，7-其他异常，8-意见与反馈 多个用逗号隔开")
    private String subType;

    @Schema(description = "操作系统，0-默认，iOS，1-Android")
    private Byte phoneSystem;

    @Schema(description = "反馈处理状态，0-默认，未处理，1-已处理")
    private Byte optStatus;

    @Schema(description = "反馈时间10位时间戳，开始时间")
    private Long ctimeStart;

    @Schema(description = "反馈时间10位时间戳，结束时间")
    private Long ctimeEnd;

    @Schema(name = "pageNum", description = "页码")
    private Integer pageNum;

    @Schema(name = "pageSize", description = "页面大小")
    private Integer pageSize;
}