package com.bilibili.miniapp.open.portal.vo.season;

import com.bilibili.miniapp.open.portal.vo.ogv.SeasonVo;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RecommendAppVo {
    private String appId;
    private String appName;
    private String appLogo;
    private SeasonVo season;
}
