package com.bilibili.miniapp.open.portal.vo.settlement;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 结算预览响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SettlementPreviewVo {

    /**
     * 申请提现金额
     */
    private String withdrawApplyAmount;

    /**
     * 税费
     */
    private String taxFee;

    /**
     * 实际提现金额
     */
    private String actualWithdrawAmount;
}
