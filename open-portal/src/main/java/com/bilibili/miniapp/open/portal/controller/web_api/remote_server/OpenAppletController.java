package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.*;
import com.bilibili.miniapp.api.service.AppletRemoteService;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AppletMapper;
import com.bilibili.miniapp.open.service.biz.applet.impl.AppletService;
import com.bilibili.miniapp.open.service.bo.applet.AppletInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortContext;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 小程序信息
 * @Date 2025/3/18
 **/

@Slf4j
@RestController
@RequestMapping("/web_api/v1/applet")
public class OpenAppletController extends AbstractController implements AppletRemoteService {

    @Resource
    private AppletService appletService;


    @PostMapping("/short")
    @Override
    public Response<List<AppletInfoDto>> batchGetShortAppletInfo(@RequestBody AppletSeasonQueryContextDto context) {
        Integer sourceFrom = context.getSourceFrom();
        List<Long> aids = context.getAids();
        List<AppletShortBo> appletShortInfoList = appletService.getAppletShortInfoList(AppletShortContext.builder()
                .aids(aids)
                .sourceFrom(sourceFrom)
                .build());
        return Response.SUCCESS(appletShortInfoList.stream().map(this::convertToAppletInfoDto).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    @PostMapping("/info")
    @Override
    public Response<AppletInfoRespDto> getAppletInfo(@RequestBody AppletInfoReqDto appletInfoReqDto) throws Exception {
        AssertUtil.notNull(appletInfoReqDto.getAppId(), ErrorCodeType.BAD_DATA.getCode(), "appId不能为空");
        AppletShortQueryParam queryParam = AppletMapper.MAPPER.toQueryParam(appletInfoReqDto);
        AppletInfoBo appletInfo = appletService.getAppletInfo(queryParam);
        return Response.SUCCESS(AppletMapper.MAPPER.toRespDto(appletInfo));
    }

    private AppletInfoDto convertToAppletInfoDto(AppletShortBo appletShortBo) {
        return AppletInfoDto.builder()
                .avid(appletShortBo.getAid())
                .seasonId(appletShortBo.getSeasonId())
                .episodeId(appletShortBo.getEpisodeId())
                .isMiniProgram(true)
                .jumpUrl(appletShortBo.getJumpUrl())
                .build();
    }
}
