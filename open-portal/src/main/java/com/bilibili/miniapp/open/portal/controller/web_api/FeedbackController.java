package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.mall.miniapp.dto.applet.AppletOpenIdInfoDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppFeedbackDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppFeedbackQuery;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.MiniAppFeedbackMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.feedback.MiniAppFeedbackQueryVo;
import com.bilibili.miniapp.open.portal.vo.feedback.MiniAppFeedbackVo;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.feedback.IMiniAppFeedbackService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.client.MainSiteAccountServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppAppletRemoteService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/11
 **/
@RestController
@RequestMapping("/web_api/v1/platform/feedback")
public class FeedbackController extends AbstractController {

    @Resource
    private IMiniAppFeedbackService miniAppFeedbackService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private MiniAppAppletRemoteService appletRemoteService;

    @Resource
    private MainSiteAccountServiceGrpcClient accountServiceGrpcClient;

    @Resource
    private IMiniAppService miniAppService;

    @MainSiteLoginValidation
    @GetMapping(value = "/list")
    public Response<PageResult<MiniAppFeedbackVo>> getMiniAppFeedbackVOS(Context context,
                                                                         @RequestParam(value = "open_id", required = false) String openId,
                                                                         @RequestParam(value = "app_id", required = false) String appId,
                                                                         @RequestParam(value = "nick_name", required = false) String nickName,
                                                                         @RequestParam(value = "sub_type", required = false) String subType,
                                                                         @RequestParam(value = "phone_system", required = false) Byte phoneSystem,
                                                                         @RequestParam(value = "opt_status", required = false) Byte optStatus,
                                                                         @RequestParam(value = "ctime_start", required = false) Long ctimeStart,
                                                                         @RequestParam(value = "ctime_end", required = false) Long ctimeEnd,
                                                                         @RequestParam(value = "page_num", defaultValue = "1") Integer pageNum,
                                                                         @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {
        MiniAppFeedbackQuery miniAppFeedbackQuery = new MiniAppFeedbackQuery();
        miniAppFeedbackQuery.setAppId(appId);
        miniAppFeedbackQuery.setNickName(nickName);
        miniAppFeedbackQuery.setSubType(subType);
        miniAppFeedbackQuery.setPhoneSystem(phoneSystem);
        miniAppFeedbackQuery.setOptStatus(optStatus);
        miniAppFeedbackQuery.setCtimeStart(ctimeStart);
        miniAppFeedbackQuery.setCtimeEnd(ctimeEnd);
        miniAppFeedbackQuery.setPageNum(pageNum);
        miniAppFeedbackQuery.setPageSize(pageSize);
        if (StringUtils.isNotBlank(openId)) {
            // 通过openId查询mid
            AppletOpenIdInfoDTO info = appletRemoteService.getUserInfoByOpenId(openId);
            // 如果info为null，或者info的type不是0（小程序），或者mid不是正数，或者appId为空，或者appId不匹配，则返回空结果
            if (Objects.isNull(info) || !Objects.equals(info.getType(), (byte) 0) || !NumberUtil.isPositive(info.getMid()) ||
                    StringUtils.isBlank(info.getAppId()) || !StringUtils.equals(info.getAppId(), appId)) {
                return Response.SUCCESS(PageResult.emptyPageResult());
            }
            miniAppFeedbackQuery.setMid(info.getMid());
        }
        List<MiniAppMemberDTO> memberDTOS = miniAppService.queryMemberInfo(context.getMid());
        if (CollectionUtils.isNotEmpty(memberDTOS)) {
            List<String> appIds = memberDTOS.stream()
                    .map(MiniAppMemberDTO::getAppId)
                    .distinct()
                    .collect(Collectors.toList());
            if (StringUtils.isNotBlank(appId)) {
                appIds.add(appId);
            }
            miniAppFeedbackQuery.setAppIds(appIds);
        }
        populateDefaultQueryParams(miniAppFeedbackQuery);
        PageResult<MiniAppFeedbackDTO> dtoPage = miniAppFeedbackService.getMiniAppFeedbackDTOS(miniAppFeedbackQuery);
        List<MiniAppFeedbackDTO> records = dtoPage.getRecords();
        if (records == null || records.isEmpty()) {
            return Response.SUCCESS(PageResult.emptyPageResult());
        }
        List<Long> mids = records.stream().map(MiniAppFeedbackDTO::getMid).distinct().collect(Collectors.toList());
        Map<Long, UserInfoBo> userInfoBoMap = accountServiceGrpcClient.queryUser(mids, GrpcCallContext.builder().timeout(2000).build());

        return Response.SUCCESS(
                new PageResult<>(
                        dtoPage.getTotal(),
                        records.stream()
                                .map(dto -> {
                                    MiniAppFeedbackVo vo = new MiniAppFeedbackVo();
                                    BeanUtils.copyProperties(dto, vo);
                                    // 设置用户信息
                                    UserInfoBo userInfo = userInfoBoMap.get(dto.getMid());
                                    if (userInfo != null) {
                                        vo.setMid(userInfo.getMid());
                                        vo.setNickName(userInfo.getName());
                                        vo.setFaceUrl(userInfo.getFace());
                                    }
                                    return vo;
                                })
                                .collect(Collectors.toList())
                )
        );
    }

    private void populateDefaultQueryParams(MiniAppFeedbackQuery miniAppFeedbackQuery) {
        // 小程序
        miniAppFeedbackQuery.setAppType((byte) 0);
        // 仅查询功能异常的，不查询投诉的
        miniAppFeedbackQuery.setType((byte) 0);
    }
}
