package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;


import com.bilibili.miniapp.api.dto.applet.AppletAuthorizationAuthorDto;
import com.bilibili.miniapp.api.dto.applet.AppletPriorityDto;
import com.bilibili.miniapp.api.dto.applet.AppletSeasonBindDto;
import com.bilibili.miniapp.api.dto.applet.AppletSeasonDto;
import com.bilibili.miniapp.api.service.MiniAppAppletPlatformRemoteService;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AppletPlatformControllerMapper;
import com.bilibili.miniapp.open.service.biz.applet.IAppletPlatformService;
import com.bilibili.miniapp.open.service.bo.applet.AppletPriorityBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AuthorSeasonQueryParam;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBaseInfoBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: huan
 * @Date: 2025-06-27 17:57
 * @Description:
 */

@RestController
@RequestMapping("/web_api/v1/platform/miniapp/applet")
public class MiniAppAppletPlatformRemoteServer extends AbstractController implements MiniAppAppletPlatformRemoteService {

    @Resource
    private IAppletPlatformService appletPlatformService;

    @Override
    @GetMapping("/season")
    public Response<PageResult<AppletSeasonDto>> queryAppletSeasonPage(
            @RequestParam("mid") Long mid,
            @RequestParam(value = "season_id", required = false) Long seasonId,
            @RequestParam(value = "app_id", required = false) String appId,
            @RequestParam(value = "page_num", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize) throws Exception {
        PageResult<AppletSeasonBo> appletSeasonList = appletPlatformService.getAppletSeasonList(AuthorSeasonQueryParam.builder()
                .mid(mid)
                .seasonId(seasonId)
                .appId(appId)
                .page(Page.valueOf(pageNum, pageSize))
                .build());
        List<AppletSeasonBo> records = appletSeasonList.getRecords();
        return Response.SUCCESS(new PageResult<>(appletSeasonList.getTotal(),
                records.stream().map(this::convertToDto).collect(Collectors.toList())));
    }

    @Override
    @GetMapping("/author")
    public Response<List<AppletAuthorizationAuthorDto>> getAppletAuthorizationAuthorInfo() {
        List<UserInfoBo> allAuthor = appletPlatformService.getAllAuthor();
        return Response.SUCCESS(allAuthor.stream().map(AppletPlatformControllerMapper.INSTANCE::toAppletAuthorizationAuthorDto).collect(Collectors.toList()));
    }


    @Override
    @PostMapping("/season/bind")
    public Response<Void> bindSeasonApplet(@RequestBody AppletSeasonBindDto appletSeasonBindDto) {
        appletPlatformService.bindAppletSeason(appletSeasonBindDto.getAppId(), appletSeasonBindDto.getSeasonId());
        return Response.SUCCESS();
    }

    @Override
    @GetMapping("/priority")
    public Response<List<AppletPriorityDto>> getAppletPriorityList() {
        List<AppletPriorityBo> appletPriority = appletPlatformService.getAppletPriority();
        return Response.SUCCESS(appletPriority.stream().map(this::convertToDto).collect(Collectors.toList()));
    }

    @Override
    @PostMapping("/priority/upload")
    public Response<Void> uploadAppletPriorityList(@RequestBody List<String> appIdList) {
        appletPlatformService.saveAppletPriority(appIdList);
        return Response.SUCCESS();
    }

    private AppletSeasonDto convertToDto(AppletSeasonBo appletSeasonBo) {
        // Convert AppletSeasonBo to AppletSeasonDto
        AppletSeasonDto.AppletSeasonDtoBuilder builder = AppletSeasonDto.builder();
        AppletSeasonInfoBo seasonBo = appletSeasonBo.getSeasonBo();
        MiniAppBaseInfoBo defaultMiniApp = seasonBo.getMiniAppBaseInfo();
        builder.seasonId(seasonBo.getSeasonId())
                .seasonName(seasonBo.getTitle())
                .appId(defaultMiniApp.getAppId())
                .appName(defaultMiniApp.getName())
                .developType(defaultMiniApp.getDevelopType())
                .seasonPaymentStatus(seasonBo.getSeasonPaymentStatus())
                .candidateApplets(appletSeasonBo.getCandidateApplets().stream()
                .map(candidate -> AppletSeasonDto.CandidateAppletDto.builder()
                        .appId(candidate.getCandidate().getAppId())
                        .appName(candidate.getCandidate().getName())
                        .developType(candidate.getCandidate().getDevelopType())
                        .defaultTag(candidate.isDefaultTag())
                        .build())
                .collect(Collectors.toList()));
        return builder.build();
    }

    private AppletPriorityDto convertToDto(AppletPriorityBo appletPriorityBo) {
        // Convert AppletPriorityBo to AppletPriorityDto
        return AppletPriorityDto.builder()
                .appId(appletPriorityBo.getAppletBaseInfo().getAppId())
                .appName(appletPriorityBo.getAppletBaseInfo().getName())
                .priority(appletPriorityBo.getPriority()).build();
    }
}
