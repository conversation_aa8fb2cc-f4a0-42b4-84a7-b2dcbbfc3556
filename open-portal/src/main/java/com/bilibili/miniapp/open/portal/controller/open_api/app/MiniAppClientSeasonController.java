package com.bilibili.miniapp.open.portal.controller.open_api.app;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.ClientSeasonControllerMapper;
import com.bilibili.miniapp.open.portal.vo.client.season.ClientSeasonVo;
import com.bilibili.miniapp.open.portal.vo.client.season.ClientUnlockEpVo;
import com.bilibili.miniapp.open.portal.vo.client.season.ClientUserSeasonVo;
import com.bilibili.miniapp.open.portal.vo.client.season.ClientUserUnlockVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.client.season.IClientSeasonUnlockService;
import com.bilibili.miniapp.open.service.biz.ogv.impl.OgvSeasonService;
import com.bilibili.miniapp.open.service.bo.client.season.ClientUserSeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/client/season")
public class MiniAppClientSeasonController extends AbstractController {

    @Autowired
    private OgvSeasonService ogvSeasonService;
    @Autowired
    private IClientSeasonUnlockService clientSeasonUnlockService;

    @GetMapping("/tab/list")
    @MiniAppValidation
    public Response<PageResult<ClientSeasonVo>> queryTabList(Context context,
                                                             @RequestParam("tab_type") Integer tabType,
                                                             @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                             @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {

        PageResult<SeasonBo> pageResult = ogvSeasonService.queryTabSeasonsForClient(context.getMiniAppId(), tabType, Page.valueOf(page, size));

        return Response.SUCCESS(
                new PageResult<>(pageResult.getTotal(),
                        pageResult.getRecords().stream()
                                .map(ClientSeasonControllerMapper.MAPPER::toVo)
                                .collect(Collectors.toList()))
        );
    }


    @MiniAppValidation
    @PostMapping("/unlock")
    public Response<Void> unlock(Context context,
                                 @RequestBody ClientUserUnlockVo clientUserUnlockVo) {

        List<ClientUnlockEpVo> epList = clientUserUnlockVo.getEpList();
        if (CollectionUtils.isEmpty(epList)) {
            return Response.SUCCESS();
        }
        List<Long> epIds = epList.stream().map(ClientUnlockEpVo::getEpId).collect(Collectors.toList());

        clientSeasonUnlockService.unlockSeason(context.getMiniAppId(),
                clientUserUnlockVo.getOpenId(),
                clientUserUnlockVo.getSeasonId(),
                epIds);

        return Response.SUCCESS();
    }

    @MiniAppValidation
    @GetMapping("/query")
    public Response<ClientUserSeasonVo> querySeason(Context context,
                                                    @RequestParam("open_id") String openId,
                                                    @RequestParam("season_id") Long seasonId) {

        ClientUserSeasonBo clientUserSeasonBo = ogvSeasonService.queryUserSeason(context.getMiniAppId(), openId, seasonId);

        return Response.SUCCESS(ClientSeasonControllerMapper.MAPPER.toVo(clientUserSeasonBo));
    }
}
