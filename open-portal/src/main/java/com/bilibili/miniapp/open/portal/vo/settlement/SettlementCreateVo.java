package com.bilibili.miniapp.open.portal.vo.settlement;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 创建结算单请求VO
 *
 * <AUTHOR>
 * @date 2025/6/6
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SettlementCreateVo {

    private String appId;

    /**
     * 汇联易预提单ID列表
     */
    private List<String> accrualIds;

}
