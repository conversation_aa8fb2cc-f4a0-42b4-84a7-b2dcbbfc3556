package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppFeedbackDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppFeedbackQuery;
import com.bilibili.miniapp.open.portal.vo.feedback.MiniAppFeedbackQueryVo;
import com.bilibili.miniapp.open.portal.vo.feedback.MiniAppFeedbackVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MiniAppFeedbackMapper {
    MiniAppFeedbackMapper MAPPER = Mappers.getMapper(MiniAppFeedbackMapper.class);

    MiniAppFeedbackQuery toMiniAppFeedbackQuery(MiniAppFeedbackQueryVo vo);

    MiniAppFeedbackVo toMiniAppFeedbackVo(MiniAppFeedbackDTO dto);
}