package com.bilibili.miniapp.open.portal.vo.feedback;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/11
 **/
@Data
@Schema(description = "反馈")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppFeedbackVo {

    @Schema(description = "反馈id")
    private Integer feedbackId;

    @Schema(description = "小程序id")
    private String appId;

    @JsonProperty(value = "vAppId")
    @Schema(description = "虚拟小程序id")
    private String vAppId;

    @Schema(description = "小程序类型，0-默认，小程序，1-小游戏，2-内部小程序")
    private Byte appType;

    @Schema(description = "小程序名称")
    private String appName;

    @Schema(description = "小程序头像url")
    private String appLogo;

    @Schema(description = "小程序线上二维码url")
    private String appUrl;

    @Schema(description = "反馈类型，0-默认，功能异常，1-投诉")
    private Byte type;

    @Schema(description = "反馈内容")
    private String content;

    @Schema(description = "反馈详细描述")
    private String description;

    @Schema(description = "联系方式")
    private String contact;

    @Schema(description = "相关截图")
    private List<String> imageList;

    @Schema(description = "用户账号mid")
    private Long mid;

    @Schema(description = "用户登录小程序标识")
    private String openId;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像url")
    private String faceUrl;

    @Schema(description = "用户性别，0-默认，保密，1-男，2-女")
    private String sex;

    @Schema(description = "用户出生年月")
    private Date birthday;

    @Schema(description = "手机系统，0-默认，iOS，1-Android")
    private Byte phoneSystem;

    @Schema(description = "手机品牌")
    private String phoneBrand;

    @Schema(description = "手机机型")
    private String phoneModel;

    @Schema(description = "app版本(build)")
    private String appVersion;

    @Schema(description = "反馈处理记录内容")
    private String optContent;

    @Schema(description = "反馈处理人")
    private String optUser;

    @Schema(description = "反馈处理时间")
    private Date optTime;

    @Schema(description = "反馈处理状态，0-默认，未处理，1-已处理")
    private Byte optStatus;

    @Schema(description = "是否删除，0默认，不删除；1删除")
    private Byte isDeleted;

    @Schema(description = "创建时间")
    private Date ctime;

    @Schema(description = "更新时间")
    private Date mtime;

    @Schema(description = "日志文件地址")
    private String logFileUrl;

    @Schema(description = "app版本(基础库版本)")
    private String systemVersion;

    @Schema(description = "渠道唯一标识")
    private String channelUuid;
}
