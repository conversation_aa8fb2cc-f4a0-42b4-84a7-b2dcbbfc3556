package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.PlatformSeasonControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.season.AddSeasonToTabVo;
import com.bilibili.miniapp.open.portal.vo.season.PlatformSeasonInfoVo;
import com.bilibili.miniapp.open.portal.vo.season.RemoveSeasonFromTabVo;
import com.bilibili.miniapp.open.portal.vo.season.SeasonTabInfoVo;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonTabService;
import com.bilibili.miniapp.open.service.bo.ogv.PlatformSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@RestController
@RequestMapping("/web_api/v1/platform/season")
public class SeasonController extends AbstractController {

    @Autowired
    private IOgvSeasonService ogvSeasonService;
    @Autowired
    private ISeasonTabService seasonTabService;

    @GetMapping("/list")
    @MainSiteLoginValidation
    public Response<PageResult<PlatformSeasonInfoVo>> querySeasons(Context context,
                                                                   @RequestParam("app_id") String appId,
                                                                   @RequestParam("up_mid") Long upMid,
                                                                   @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                   @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {


        PageResult<PlatformSeasonInfoBo> pageResult = ogvSeasonService.querySeasons(appId, upMid, Page.valueOf(page, size));

        return Response.SUCCESS(
                new PageResult<>(pageResult.getTotal(),
                        pageResult.getRecords().stream()
                                .map(PlatformSeasonControllerMapper.MAPPER::toVo)
                                .collect(Collectors.toList()))
        );
    }


    @PostMapping("/tab")
    @MainSiteLoginValidation
    public Response<Void> addSeasonsToTab(Context context,
                                          @RequestBody AddSeasonToTabVo addSeasonToTabVo) {

        List<SeasonTabInfoVo> seasonList = addSeasonToTabVo.getSeasonList();
        if (CollectionUtils.isEmpty(seasonList)) {
            return Response.SUCCESS();
        }

        ogvSeasonService.addSeasonToTab(addSeasonToTabVo.getAppId(),
                addSeasonToTabVo.getTabType(),
                seasonList.stream()
                        .map(SeasonTabInfoVo::getSeasonId)
                        .collect(Collectors.toList()));

        return Response.SUCCESS();
    }

    @DeleteMapping("/tab")
    @MainSiteLoginValidation
    public Response<Void> removeSeasonsToTab(Context context,
                                          @RequestBody RemoveSeasonFromTabVo removeSeasonFromTabVo) {


        seasonTabService.removeSeasonFromTab(removeSeasonFromTabVo.getAppId(),
                removeSeasonFromTabVo.getTabType(),
                removeSeasonFromTabVo.getSeasonId());

        return Response.SUCCESS();
    }

    @GetMapping("/tab/list")
    @MainSiteLoginValidation
    public Response<PageResult<PlatformSeasonInfoVo>> tabSeasonList(Context context,
                                                                    @RequestParam("app_id") String appId,
                                                                    @RequestParam("tab_type") Integer tabType,
                                                                    @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                    @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {

        PageResult<SeasonBo> pageResult = ogvSeasonService.queryTabSeasonsForPlatform(appId, tabType, Page.valueOf(page, size));

        return Response.SUCCESS(
                new PageResult<>(pageResult.getTotal(),
                        pageResult.getRecords().stream()
                                .map(PlatformSeasonControllerMapper.MAPPER::toVo)
                                .collect(Collectors.toList()))
        );
    }
}
