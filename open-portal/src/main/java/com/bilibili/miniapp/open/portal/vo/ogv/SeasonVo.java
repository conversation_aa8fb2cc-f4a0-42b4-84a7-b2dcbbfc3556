package com.bilibili.miniapp.open.portal.vo.ogv;

import com.bilibili.miniapp.open.portal.vo.user.UserVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 剧信息
 *
 * <AUTHOR>
 * @date 2024/12/10 17:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SeasonVo implements Serializable {
    private static final long serialVersionUID = 1702535683954066964L;
    @Schema(description = "ssid")
    private Long seasonId;
    @Schema(description = "剧集封面")
    private String cover;
    @Schema(description = "剧集标题")
    private String title;
    @Schema(description = "剧集子标题")
    private String subTitle;
    @Schema(description = "媒资风格")
    private List<String> styles;
    @Schema(description = "是否已完结，1：已完结，0：未完结，-1：未知")
    private Integer isFinish;
    @Schema(description = "作者（投稿人）")
    private UserVo author;
    @Schema(description = "集信息")
    private List<EpisodeVo> episodes;

    private Integer epCount;
}
