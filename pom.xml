<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.bilibili.miniapp</groupId>
    <artifactId>open-platform</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>miniapp-open-platform</name>
    <description>miniapp-open-platform</description>
    <packaging>pom</packaging>


    <modules>
        <module>open-portal</module>
        <module>open-service</module>
        <module>open-common</module>
        <module>open-repository</module>
        <module>open-doc</module>
        <module>open-api</module>
    </modules>

    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <warp.version>1.2.1</warp.version>
        <pleiades.version>1.3.3</pleiades.version>
        <lombok.version>1.18.34</lombok.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-colletions.version>4.4</commons-colletions.version>
        <slf4j.version>2.0.4</slf4j.version>
        <httpclient.version>4.5.14</httpclient.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring.version>5.3.31</spring.version>
        <springdoc.version>1.8.0</springdoc.version>
        <idatabus.starter.version>1.1.2-RELEASE</idatabus.starter.version>
        <cat.starter.version>1.0.0-RELEASE</cat.starter.version>
        <xxl.version>bili-2.5.6-RELEASE</xxl.version>
        <pagehelper.version>5.0.1</pagehelper.version>
        <commons-pool2.version>2.12.0</commons-pool2.version>
        <guava.version>23.0</guava.version>
        <gson.version>2.10.1</gson.version>
        <okhttp.version>4.12.0</okhttp.version>
        <jsoup.version>1.18.1</jsoup.version>
        <fastjson.version>1.2.83</fastjson.version>
        <caffeine.version>3.1.8</caffeine.version>
        <mybatis.version>3.5.10</mybatis.version>
        <mybatis-spring.version>3.0.2</mybatis-spring.version>
        <miniapp-api.version>2.2.71</miniapp-api.version>
        <miniapp.open.api.version>0.0.9_applet-SNAPSHOT</miniapp.open.api.version>
        <jackson.version>2.17.2</jackson.version>
        <amazon-awssdk.version>2.26.8</amazon-awssdk.version>
        <regulation.api.version>1.0.0-SNAPSHOT</regulation.api.version>
        <abyss-version>1.26.1</abyss-version>
        <nebula-version>1.16.3-SNAPSHOT</nebula-version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.17.28</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.26</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>warp-bom</artifactId>
                <version>${warp.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>pleiades.bom</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${pleiades.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
                <!-- 默认包含以下子依赖 -->
                <!-- junit-jupiter | mockito-core | assertj-core | jsonassert | ... -->
                <exclusions>
                    <!-- 如需使用更新版本可排除旧版本 -->
                    <exclusion>
                        <groupId>org.junit.vintage</groupId>
                        <artifactId>junit-vintage-engine</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- === 增强测试工具 === -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>5.2.0</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>3.24.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>5.10.0</version>
                <scope>test</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${amazon-awssdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.bilibili.regulation</groupId>
                <artifactId>regulation-api</artifactId>
                <version>${regulation.api.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>bili-nexus-release-server</id>
            <url>https://nexus.bilibili.co/content/repositories/releases</url>
        </repository>

        <snapshotRepository>
            <id>bili-nexus-snapshots-server</id>
            <url>https://nexus.bilibili.co/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
