package com.bilibili.miniapp.api.dto.applet;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: huan
 * @Date: 2025-07-01 14:09
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppletAuthorizationAuthorDto {

    private Long mid;

    private String nickName;

    private String face;
}
