package com.bilibili.miniapp.api.service;


import com.bilibili.miniapp.api.dto.applet.AppletAuthorizationAuthorDto;
import com.bilibili.miniapp.api.dto.applet.AppletPriorityDto;
import com.bilibili.miniapp.api.dto.applet.AppletSeasonBindDto;
import com.bilibili.miniapp.api.dto.applet.AppletSeasonDto;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;

import java.util.List;

/**
 * @author: huan
 * @Date: 2025-07-01 14:00
 * @Description:
 */
public interface MiniAppAppletPlatformRemoteService {

    /**
     * 查询小程序短剧列表
     * @param mid
     * @param seasonId
     * @param appId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Response<PageResult<AppletSeasonDto>> queryAppletSeasonPage(
            Long mid,
            Long seasonId,
            String appId,
            Integer pageNum,
            Integer pageSize
    ) throws Exception;

    /**
     * 获取所有的授权小程序的作者信息
     * @return
     */
    Response<List<AppletAuthorizationAuthorDto>> getAppletAuthorizationAuthorInfo();

    /**
     * 绑定小程序剧集
     * @return
     */
    Response<Void> bindSeasonApplet(AppletSeasonBindDto appletSeasonBindDto);

    /**
     * 获取小程序优先级列表
     * @return
     */
    Response<List<AppletPriorityDto>> getAppletPriorityList();

    /**
     * 上传小程序优先级列表
     * @param appIdList
     * @return
     */
    Response<Void> uploadAppletPriorityList(List<String> appIdList);
}
