package com.bilibili.miniapp.api.dto.applet;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: huan
 * @Date: 2025-07-01 14:03
 * @Description:
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppletSeasonDto {

    private Long seasonId;

    private String seasonName;

    private String appId;

    private String appName;

    private Integer seasonPaymentStatus;

    private Integer developType;

    private List<CandidateAppletDto> candidateApplets;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CandidateAppletDto {
        private String appId;

        private String appName;

        private Integer developType;

        private boolean defaultTag;
    }
}
